<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大型活动管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #336579; /* 设置背景颜色为深青色 */
        }
        .container {
            color: #95D7E3; /* 设置文字颜色为白色 */
        }
        .card {
            background-color: #102735; /* 设置卡片背景颜色为深灰色 */
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .option-group, .sub-options {
            background-color: #4a5a65; /* 设置选项组背景颜色为稍浅的灰色 */
            border-radius: 8px;
            padding: 10px;
        }
        .action-buttons button {
            background-color: #538995; /* 设置按钮背景颜色 */
            color: #ffffff;
            border: 2px solid #569DA3; /* 新增：按钮边框颜色 */
        }
        .action-buttons button:hover {
            background-color: #CEB534; /* 设置按钮悬停时的背景颜色 */
            color: #102735;
        }
        .action-buttons {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }
        .load-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .load-btn:hover {
            background-color: #5a6268;
        }
        .run-btn {
            width: 200px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>大型活动管理系统</h1>
        </header>
        
        <main>
            <section class="card" id="networkSection">
                <h2>仿真路网（单选）</h2>
                <div class="option-group">
                    <div class="option">
                        <input type="radio" id="net-preset" name="network" value="preset" checked>
                        <label for="net-preset">预设</label>
                    </div>
                    <div class="option">
                        <input type="radio" id="net-custom" name="network" value="custom">
                        <label for="net-custom">自定义</label>
                    </div>
                    <button class="upload-btn" id="uploadNetBtn">
                        <i class="bi bi-upload"></i> 加载文件(*.net.xml)
                    </button>
                </div>
            </section>

            <section class="card" id="trafficSection">
                <h2>交通需求（单选）</h2>
                <div class="option-group">
                    <div class="option">
                        <input type="radio" id="traffic-preset" name="traffic" value="preset" checked>
                        <label for="traffic-preset">预设</label>
                    </div>
                    <div class="option">
                        <input type="radio" id="traffic-custom" name="traffic" value="custom">
                        <label for="traffic-custom">自定义</label>
                    </div>
                    <button class="upload-btn" id="uploadRouBtn">
                        <i class="bi bi-upload"></i> 加载文件(*.rou.xml)
                    </button>
                </div>
                <div class="sub-options">
                    <div class="sub-option">
                        <span class="bullet">•</span>
                        <label>车辆类型：</label>
                        <div class="select-wrapper">
                            <select id="vehicleType">
                                <option value="general">一般车辆</option>
                                <option value="vip">贵宾专车</option>
                            </select>
                        </div>
                    </div>
                    <div class="sub-option">
                        <span class="bullet">•</span>
                        <label>组织时段：</label>
                        <div class="select-wrapper">
                            <select id="timePhase">
                                <option value="entrance">进场</option>
                                <option value="exit">离场</option>
                            </select>
                        </div>
                    </div>
                </div>
            </section>

            <section class="card" id="organizationSection">
                <h2>组织方案</h2>
                <div class="sub-options">
                    <div class="org-grid">
                        <div class="sub-option">
                            <span class="bullet">•</span>
                            <label>出入口方案：</label>
                            <div class="select-wrapper">
                                <select id="entranceType">
                                    <option value="east">仅开放东侧出入口</option>
                                    <option value="south">仅开放南侧出入口</option>
                                    <option value="all">全部开放</option>
                                </select>
                            </div>
                        </div>
                        <div class="sub-option">
                            <span class="bullet">•</span>
                            <label>贵宾专车优先通行：</label>
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="vipPriority">
                            </div>
                        </div>
                        <div class="sub-option">
                            <span class="bullet">•</span>
                            <label>道路限行：</label>
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="roadRestriction">
                            </div>
                        </div>
                        <div class="sub-option">
                            <span class="bullet">•</span>
                            <label>信号配时（单选）：</label>
                            <div class="option-group compact">
                                <div class="option">
                                    <input type="radio" id="signal-preset" name="signal" value="preset" checked>
                                    <label for="signal-preset">预设</label>
                                </div>
                                <div class="option">
                                    <input type="radio" id="signal-custom" name="signal" value="custom">
                                    <label for="signal-custom">自定义</label>
                                </div>
                                <button class="upload-btn" id="uploadAddBtn">
                                    <i class="bi bi-upload"></i> 加载文件(*.add.xml)
                                </button>
                            </div>
                            <div class="sub-option indent">
                                <span class="bullet">•</span>
                                <label>信控优化：</label>
                                <div class="checkbox-wrapper">
                                    <input type="checkbox" id="signalOptimization">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div class="action-buttons">
                <button class="run-btn">运行方案</button>
                <button class="load-btn" id="loadResultBtn">
                    <i class="bi bi-file-earmark-text"></i> 加载已有结果
                </button>
            </div>
        </main>
    </div>
    
    <script src="script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 构建配置JSON - 供script.js中的startSimulation函数使用
            window.buildConfigJSON = function() {
                // 获取路网配置
                const networkType = document.querySelector('input[name="network"]:checked').value;
                const entranceType = document.getElementById('entranceType').value;
                const roadRestriction = document.getElementById('roadRestriction').checked;
                
                // 获取信号配置
                const signalType = document.querySelector('input[name="signal"]:checked').value;
                const signalOptimization = document.getElementById('signalOptimization').checked;
                
                // 获取交通需求配置
                const trafficType = document.querySelector('input[name="traffic"]:checked').value;
                const vehicleType = document.getElementById('vehicleType').value;
                const timePhase = document.getElementById('timePhase').value;
                const vipPriority = document.getElementById('vipPriority').checked;
                
                // 构建完整配置对象
                return {
                    "network_config": {
                        "type": networkType === "preset" ? "predefined" : "custom",
                        "file_path": networkType === "custom" ? "custom_network.net.xml" : null,
                        "entrance_plan": entranceTypeToText(entranceType),
                        "road_restriction": { 
                            "enabled": roadRestriction, 
                            "restricted_edges": roadRestriction ? getRestrictedEdges() : [] 
                        }
                    },
                    "signal_config": {
                        "type": signalType === "preset" ? "predefined" : "custom",
                        "file_path": signalType === "custom" ? "custom_signal.add.xml" : null,
                        "optimization": { 
                            "enabled": signalOptimization, 
                            "selected_intersections": signalOptimization ? getSelectedIntersections() : [] 
                        }
                    },
                    "traffic_config": {
                        "type": trafficType === "preset" ? "predefined" : "custom",
                        "file_path": trafficType === "custom" ? "custom_traffic.rou.xml" : null,
                        "scenario": timePhaseToText(timePhase),
                        "vehicle_type": vehicleTypeToText(vehicleType),
                        "vip_priority": { 
                            "enabled": vipPriority 
                        }
                    }
                };
            }
            
            // 辅助函数，转换出入口选择为文本
            function entranceTypeToText(type) {
                switch(type) {
                    case "east": return "仅开放东侧出入口";
                    case "south": return "仅开放南侧出入口";
                    case "all": return "全部开放";
                    default: return "仅开放东侧出入口";
                }
            }
            
            // 辅助函数，转换车辆类型选择为文本
            function vehicleTypeToText(type) {
                switch(type) {
                    case "general": return "仅一般车辆";
                    case "vip": return "存在贵宾专车";
                    default: return "仅一般车辆";
                }
            }
            
            // 辅助函数，转换时段选择为文本
            function timePhaseToText(phase) {
                switch(phase) {
                    case "entrance": return "进场";
                    case "exit": return "离场";
                    default: return "进场";
                }
            }
            
            // 获取已选择的交叉口列表（从全局状态获取）
            function getSelectedIntersections() {
                // 从script.js中的全局变量获取
                return window.selectedIntersections || [];
            }
            
            // 获取已选择的限行路段列表（从全局状态获取）
            function getRestrictedEdges() {
                // 从script.js中的全局变量获取
                return window.selectedRestrictedEdges || [];
            }
            
            // 导航到结果页面并传递数据 - 供script.js使用
            window.navigateToResultPage = function(data) {
                // 构建结果数据，匹配results.html期望的格式
                const resultData = {
                    "simulation_id": data.simulation_id,
                    "config_summary": data.config_summary,
                    "start_time": new Date().toISOString(),  // 使用当前时间
                    "simulation_results": {
                        "pedestrian_metrics": data.metrics.pedestrian_metrics,
                        "vehicle_metrics": data.metrics.vehicle_metrics,
                        "vip_vehicle_metrics": data.metrics.vip_vehicle_metrics,
                        "venue_area_metrics": data.metrics.venue_area_metrics
                    }
                };
                
                // 将数据编码为URL参数
                const encodedData = encodeURIComponent(JSON.stringify(resultData));
                
                // 跳转到结果页面
                window.location.href = `results.html?data=${encodedData}`;
            }
            
            // 显示错误信息 - 供script.js使用
            window.showError = function(message) {
                alert(`错误: ${message}`);
            }
        });
    </script>
</body>
</html> 