import os
import json
from typing import Dict, List, Any, Tuple

class ConfigParser:
    """配置解析器，负责验证新的JSON配置格式并提供配置访问接口"""
    
    def __init__(self):
        self.base_dir = 'sumo_data'
        self.template_dir = os.path.join(self.base_dir, 'templates')
        
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证配置的有效性
        
        参数:
        config: dict - 新格式的JSON配置
        
        返回:
        dict - 验证结果 {'valid': bool, 'errors': list}
        """
        errors = []
        
        try:
            # 1. 验证顶层结构
            required_sections = ['network_config', 'signal_config', 'traffic_config']
            for section in required_sections:
                if section not in config:
                    errors.append(f"缺少必需的配置模块: {section}")
            
            if errors:
                return {'valid': False, 'errors': errors}
                
            # 2. 验证网络配置
            network_errors = self._validate_network_config(config['network_config'])
            errors.extend(network_errors)
            
            # 3. 验证信号配置
            signal_errors = self._validate_signal_config(config['signal_config'])
            errors.extend(signal_errors)
            
            # 4. 验证交通配置  
            traffic_errors = self._validate_traffic_config(config['traffic_config'])
            errors.extend(traffic_errors)
            
            # 5. 验证业务逻辑约束
            logic_errors = self._validate_business_logic(config)
            errors.extend(logic_errors)
            
            return {'valid': len(errors) == 0, 'errors': errors}
            
        except Exception as e:
            return {'valid': False, 'errors': [f'配置验证过程出错: {str(e)}']}
    
    def _validate_network_config(self, network_config: Dict[str, Any]) -> List[str]:
        """验证网络配置"""
        errors = []
        
        # 验证type字段
        if 'type' not in network_config:
            errors.append("network_config缺少type字段")
            return errors
            
        config_type = network_config['type']
        if config_type not in ['predefined', 'custom']:
            errors.append(f"network_config.type无效值: {config_type}")
            
        # 验证条件字段
        if config_type == 'predefined':
            if network_config.get('file_path') is not None:
                errors.append("预设路网配置时file_path必须为null")
                
            if 'entrance_plan' not in network_config:
                errors.append("预设路网配置缺少entrance_plan字段")
            elif network_config['entrance_plan'] not in ['仅开放东侧出入口', '仅开放南侧出入口', '全部开放']:
                errors.append(f"无效的entrance_plan值: {network_config['entrance_plan']}")
                
        elif config_type == 'custom':
            if not network_config.get('file_path'):
                errors.append("自定义路网配置必须提供file_path")
            elif not network_config['file_path'].endswith('.net.xml'):
                errors.append("自定义路网文件必须是.net.xml格式")
            elif not os.path.exists(network_config['file_path']):
                errors.append(f"自定义路网文件不存在: {network_config['file_path']}")
        
        # 验证道路限行配置
        if 'road_restriction' in network_config:
            restriction = network_config['road_restriction']
            if 'enabled' not in restriction:
                errors.append("road_restriction缺少enabled字段")
            elif restriction.get('enabled') and not restriction.get('restricted_edges'):
                errors.append("启用道路限行时必须提供restricted_edges列表")
                
        return errors
    
    def _validate_signal_config(self, signal_config: Dict[str, Any]) -> List[str]:
        """验证信号配置"""
        errors = []
        
        # 验证type字段
        if 'type' not in signal_config:
            errors.append("signal_config缺少type字段")
            return errors
            
        config_type = signal_config['type']
        if config_type not in ['predefined', 'custom']:
            errors.append(f"signal_config.type无效值: {config_type}")
            
        # 验证条件字段
        if config_type == 'predefined':
            if signal_config.get('file_path') is not None:
                errors.append("预设信号配置时file_path必须为null")
        elif config_type == 'custom':
            if not signal_config.get('file_path'):
                errors.append("自定义信号配置必须提供file_path")
            elif not signal_config['file_path'].endswith('.add.xml'):
                errors.append("自定义信号文件必须是.add.xml格式")
            elif not os.path.exists(signal_config['file_path']):
                errors.append(f"自定义信号文件不存在: {signal_config['file_path']}")
        
        # 验证优化配置
        if 'optimization' in signal_config:
            optimization = signal_config['optimization']
            if 'enabled' not in optimization:
                errors.append("signal optimization缺少enabled字段")
            elif optimization.get('enabled') and not optimization.get('selected_intersections'):
                errors.append("启用信号优化时必须提供selected_intersections列表")
                
        return errors
    
    def _validate_traffic_config(self, traffic_config: Dict[str, Any]) -> List[str]:
        """验证交通配置"""
        errors = []
        
        # 验证type字段
        if 'type' not in traffic_config:
            errors.append("traffic_config缺少type字段")
            return errors
            
        config_type = traffic_config['type']
        if config_type not in ['predefined', 'custom']:
            errors.append(f"traffic_config.type无效值: {config_type}")
            
        # 验证条件字段
        if config_type == 'predefined':
            if traffic_config.get('file_path') is not None:
                errors.append("预设交通配置时file_path必须为null")
                
            if 'scenario' not in traffic_config:
                errors.append("预设交通配置缺少scenario字段")
            elif traffic_config['scenario'] not in ['进场', '离场']:
                errors.append(f"无效的scenario值: {traffic_config['scenario']}")
                
            if 'vehicle_type' not in traffic_config:
                errors.append("预设交通配置缺少vehicle_type字段")
            elif traffic_config['vehicle_type'] not in ['仅一般车辆', '存在贵宾专车']:
                errors.append(f"无效的vehicle_type值: {traffic_config['vehicle_type']}")
                
        elif config_type == 'custom':
            if not traffic_config.get('file_path'):
                errors.append("自定义交通配置必须提供file_path")
            elif not traffic_config['file_path'].endswith('.rou.xml'):
                errors.append("自定义交通文件必须是.rou.xml格式")
            elif not os.path.exists(traffic_config['file_path']):
                errors.append(f"自定义交通文件不存在: {traffic_config['file_path']}")
        
        return errors
    
    def _validate_business_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证业务逻辑约束"""
        errors = []
        
        traffic_config = config.get('traffic_config', {})
        
        # VIP优先通行约束
        if 'vip_priority' in traffic_config:
            vip_priority = traffic_config['vip_priority']
            vehicle_type = traffic_config.get('vehicle_type')
            
            if vip_priority.get('enabled') and vehicle_type != '存在贵宾专车':
                errors.append("VIP优先通行需要车辆类型为'存在贵宾专车'")
        
        return errors
    
    def get_network_file(self, config: Dict[str, Any]) -> str:
        """
        获取路网文件路径
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 路网文件路径
        """
        network_config = config['network_config']
        
        if network_config['type'] == 'custom':
            return network_config['file_path']
        else:
            return os.path.join(self.template_dir, 'gym_tls.net.xml')
    
    def get_signal_files(self, config: Dict[str, Any]) -> List[str]:
        """
        获取信号配时文件列表
        
        参数:
        config: dict - 完整配置
        
        返回:
        List[str] - 信号配时文件路径列表
        """
        signal_config = config['signal_config']
        
        if signal_config['type'] == 'custom':
            # 使用自定义信号文件
            return [signal_config['file_path']]
        else:
            return [os.path.join(self.template_dir, 'newtls.add.xml')]
    
    def get_route_file(self, config: Dict[str, Any]) -> str:
        """
        获取路由文件路径
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 路由文件路径（如果是预设配置则返回None，需要动态生成）
        """
        traffic_config = config['traffic_config']
        
        if traffic_config['type'] == 'custom':
            return traffic_config['file_path']
        else:
            # 预设交通需要动态生成
            return None
    
    def is_vip_scenario(self, config: Dict[str, Any]) -> bool:
        """
        判断是否为VIP场景
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否为VIP场景
        """
        traffic_config = config['traffic_config']
        
        if traffic_config['type'] == 'predefined':
            return traffic_config.get('vehicle_type') == '存在贵宾专车'
        
        return False
    
    def is_vip_priority_enabled(self, config: Dict[str, Any]) -> bool:
        """
        判断是否启用VIP优先通行
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否启用VIP优先通行
        """
        traffic_config = config['traffic_config']
        
        return (self.is_vip_scenario(config) and 
                traffic_config.get('vip_priority', {}).get('enabled', False))
    
    def get_scenario_type(self, config: Dict[str, Any]) -> str:
        """
        获取场景类型（进场/离场）
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 场景类型
        """
        traffic_config = config['traffic_config']
        
        if traffic_config['type'] == 'predefined':
            return traffic_config.get('scenario', '进场')
        
        return '进场'  # 自定义配置默认为进场
    
    def get_entrance_plan(self, config: Dict[str, Any]) -> str:
        """
        获取出入口方案
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 出入口方案
        """
        network_config = config['network_config']
        
        if network_config['type'] == 'predefined':
            return network_config.get('entrance_plan', '全部开放')
        
        return '全部开放'  # 自定义路网默认为全部开放
    
    def is_road_restriction_enabled(self, config: Dict[str, Any]) -> bool:
        """
        判断是否启用道路限行
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否启用道路限行
        """
        network_config = config['network_config']
        
        return network_config.get('road_restriction', {}).get('enabled', False)
    
    def get_restricted_edges(self, config: Dict[str, Any]) -> List[str]:
        """
        获取限行路段列表
        
        参数:
        config: dict - 完整配置
        
        返回:
        List[str] - 限行路段ID列表
        """
        network_config = config['network_config']
        
        if self.is_road_restriction_enabled(config):
            return network_config['road_restriction'].get('restricted_edges', [])
        
        return []
    
    def get_optimization_intersections(self, config: Dict[str, Any]) -> List[str]:
        """
        获取需要优化的交叉口列表
        
        参数:
        config: dict - 完整配置
        
        返回:
        List[str] - 交叉口ID列表
        """
        signal_config = config['signal_config']
        
        optimization = signal_config.get('optimization', {})
        if optimization.get('enabled'):
            return optimization.get('selected_intersections', [])
        
        return []
    
    def is_signal_optimization_enabled(self, config: Dict[str, Any]) -> bool:
        """
        判断是否启用信号优化
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否启用信号优化
        """
        signal_config = config['signal_config']
        
        return signal_config.get('optimization', {}).get('enabled', False)
    
    def generate_config_summary(self, config: Dict[str, Any]) -> Dict[str, str]:
        """
        生成配置摘要
        
        参数:
        config: dict - 配置数据
        
        返回:
        dict - 配置摘要
        """
        network_config = config['network_config']
        signal_config = config['signal_config']
        traffic_config = config['traffic_config']
        
        # 网络摘要
        if network_config['type'] == 'predefined':
            network_summary = f"预设路网 - {network_config.get('entrance_plan', '未指定')}"
            if network_config.get('road_restriction', {}).get('enabled'):
                restricted_count = len(network_config['road_restriction'].get('restricted_edges', []))
                network_summary += f" + 道路限行({restricted_count}条路段)"
        else:
            file_name = os.path.basename(network_config.get('file_path', '未知文件'))
            network_summary = f"自定义路网 - {file_name}"
            
        # 信号摘要
        if signal_config['type'] == 'predefined':
            signal_summary = "预设配时"
            signal_opt = signal_config.get('optimization', {})
            if signal_opt.get('enabled'):
                intersection_count = len(signal_opt.get('selected_intersections', []))
                signal_summary += f" + 自定义优化({intersection_count}个交叉口)"
        else:
            file_name = os.path.basename(signal_config.get('file_path', '未知文件'))
            signal_summary = f"自定义配时 - {file_name}"
            
        # 交通摘要
        if traffic_config['type'] == 'predefined':
            scenario = traffic_config.get('scenario', '未指定')
            vehicle_type = traffic_config.get('vehicle_type', '未指定')
            traffic_summary = f"预设需求 - {scenario}场景"
            if vehicle_type == '存在贵宾专车':
                traffic_summary += " + 贵宾专车"
                if traffic_config.get('vip_priority', {}).get('enabled'):
                    traffic_summary += "(优先通行)"
        else:
            file_name = os.path.basename(traffic_config.get('file_path', '未知文件'))
            traffic_summary = f"自定义需求 - {file_name}"
            
        return {
            'network': network_summary,
            'signal': signal_summary,
            'traffic': traffic_summary
        } 