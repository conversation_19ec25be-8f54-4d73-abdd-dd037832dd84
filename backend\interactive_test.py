#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式自定义测试脚本
支持通过交互选择配置，调用交叉口/路段选择器，自动生成JSON并发送给服务器执行仿真
"""

import json
import requests
import sys
import os
from typing import Dict, List, Any

# 添加tools目录到路径，以便导入network_selector
tools_path = os.path.join(os.path.dirname(__file__), 'tools')
if tools_path not in sys.path:
    sys.path.append(tools_path)

from network_selector.network_selector import IntersectionSelector, EdgeSelector, MixedSelector

class InteractiveTestGenerator:
    """交互式测试生成器"""
    
    def __init__(self):
        self.config = {}
        self.server_url = "http://localhost:8888"
        self.net_file_path = "sumo_data/templates/gym_tls.net.xml"
        
        # 预定义的配置选项
        self.entrance_plans = {
            "1": "仅开放东侧出入口",
            "2": "仅开放南侧出入口", 
            "3": "全部开放"
        }
        
        self.scenarios = {
            "1": "进场",
            "2": "离场"
        }
        
        self.vehicle_types = {
            "1": "仅一般车辆",
            "2": "存在贵宾专车"
        }
    
    def print_header(self):
        """打印标题"""
        print("\n" + "="*60)
        print("🚗 交互式仿真配置生成器")
        print("="*60)
        print("此工具将帮助您:")
        print("1. 交互式选择仿真配置")
        print("2. 可视化选择交叉口和路段")
        print("3. 自动生成JSON配置")
        print("4. 发送到服务器执行仿真")
        print("="*60)
    
    def get_user_choice(self, prompt: str, options: Dict[str, str], allow_empty: bool = False) -> str:
        """获取用户选择"""
        print(f"\n{prompt}")
        for key, value in options.items():
            print(f"  {key}. {value}")
        
        if allow_empty:
            print("  (直接按回车跳过)")
        
        while True:
            choice = input("请选择: ").strip()
            
            if allow_empty and choice == "":
                return ""
            
            if choice in options:
                return options[choice]
            
            print("❌ 无效选择，请重新输入")
    
    def get_yes_no(self, prompt: str) -> bool:
        """获取是/否选择"""
        while True:
            choice = input(f"{prompt} (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            print("❌ 请输入 y/yes/是 或 n/no/否")
    
    def get_current_network_file(self) -> str:
        """获取当前应该使用的路网文件路径"""
        if "network_config" in self.config:
            network_config = self.config["network_config"]
            if network_config["type"] == "custom" and network_config["file_path"]:
                # 使用自定义路网文件
                custom_path = network_config["file_path"]
                if os.path.exists(custom_path):
                    return custom_path
                else:
                    print(f"⚠️ 自定义路网文件不存在: {custom_path}")
                    print(f"🔄 使用默认路网文件: {self.net_file_path}")
        
        # 使用默认路网文件
        return self.net_file_path
    
    def configure_network(self):
        """配置路网"""
        print("\n🌐 路网配置")
        print("-" * 30)
        
        # 路网类型
        network_type = self.get_user_choice(
            "选择路网类型:",
            {"1": "predefined", "2": "custom"}
        )
        
        self.config["network_config"] = {
            "type": network_type,
            "file_path": None
        }
        
        if network_type == "predefined":
            # 出入口方案
            entrance_plan = self.get_user_choice(
                "选择出入口方案:",
                self.entrance_plans
            )
            self.config["network_config"]["entrance_plan"] = entrance_plan
        else:
            # 自定义文件路径
            file_path = input("请输入自定义路网文件路径: ").strip()
            self.config["network_config"]["file_path"] = file_path
            self.config["network_config"]["entrance_plan"] = None
        
        # 道路限行配置
        restriction_enabled = self.get_yes_no("是否启用道路限行?")
        
        self.config["network_config"]["road_restriction"] = {
            "enabled": restriction_enabled,
            "restricted_edges": []
        }
        
        if restriction_enabled:
            print("\n📍 即将打开路段选择器，请在浏览器中选择限行路段...")
            input("按回车键继续...")
            
            # 调用路段选择器 - 使用当前配置的路网文件
            current_net_file = self.get_current_network_file()
            print(f"🗂️ 使用路网文件: {current_net_file}")
            edge_selector = EdgeSelector(current_net_file)
            selected_edges = edge_selector.run(port=5001)
            
            if isinstance(selected_edges, list) and selected_edges:
                self.config["network_config"]["road_restriction"]["restricted_edges"] = selected_edges
                print(f"✅ 已选择 {len(selected_edges)} 个限行路段")
            else:
                print("⚠️ 未选择任何路段，道路限行将被禁用")
                self.config["network_config"]["road_restriction"]["enabled"] = False
    
    def configure_signal(self):
        """配置信号"""
        print("\n🚦 信号配时配置")
        print("-" * 30)
        
        # 信号类型
        signal_type = self.get_user_choice(
            "选择信号配时类型:",
            {"1": "predefined", "2": "custom"}
        )
        
        self.config["signal_config"] = {
            "type": signal_type,
            "file_path": None
        }
        
        if signal_type == "custom":
            file_path = input("请输入自定义信号配时文件路径: ").strip()
            self.config["signal_config"]["file_path"] = file_path
        
        # 信号优化配置
        optimization_enabled = self.get_yes_no("是否启用信号优化?")
        
        self.config["signal_config"]["optimization"] = {
            "enabled": optimization_enabled,
            "selected_intersections": []
        }
        
        if optimization_enabled:
            print("\n🚦 即将打开交叉口选择器，请在浏览器中选择待优化的交叉口...")
            input("按回车键继续...")
            
            # 调用交叉口选择器 - 使用当前配置的路网文件
            current_net_file = self.get_current_network_file()
            print(f"🗂️ 使用路网文件: {current_net_file}")
            intersection_selector = IntersectionSelector(current_net_file)
            selected_intersections = intersection_selector.run(port=5002)
            
            if isinstance(selected_intersections, list) and selected_intersections:
                self.config["signal_config"]["optimization"]["selected_intersections"] = selected_intersections
                print(f"✅ 已选择 {len(selected_intersections)} 个交叉口进行优化")
            else:
                print("⚠️ 未选择任何交叉口，信号优化将被禁用")
                self.config["signal_config"]["optimization"]["enabled"] = False
    
    def configure_traffic(self):
        """配置交通需求"""
        print("\n🚗 交通需求配置")
        print("-" * 30)
        
        # 交通需求类型
        traffic_type = self.get_user_choice(
            "选择交通需求类型:",
            {"1": "predefined", "2": "custom"}
        )
        
        self.config["traffic_config"] = {
            "type": traffic_type,
            "file_path": None
        }
        
        if traffic_type == "predefined":
            # 场景选择
            scenario = self.get_user_choice(
                "选择组织时段:",
                self.scenarios
            )
            self.config["traffic_config"]["scenario"] = scenario
            
            # 车辆类型
            vehicle_type = self.get_user_choice(
                "选择车辆类型:",
                self.vehicle_types
            )
            self.config["traffic_config"]["vehicle_type"] = vehicle_type
            
            # VIP优先通行配置
            vip_enabled = False
            if vehicle_type == "存在贵宾专车":
                vip_enabled = self.get_yes_no("是否启用贵宾专车优先通行?")
            
            self.config["traffic_config"]["vip_priority"] = {
                "enabled": vip_enabled
            }
        else:
            # 自定义文件
            file_path = input("请输入自定义交通需求文件路径: ").strip()
            self.config["traffic_config"]["file_path"] = file_path
            self.config["traffic_config"]["scenario"] = None
            self.config["traffic_config"]["vehicle_type"] = None
            self.config["traffic_config"]["vip_priority"] = {"enabled": False}
    
    def display_config_summary(self):
        """显示配置摘要"""
        print("\n📋 配置摘要")
        print("="*50)
        
        # 路网配置
        net_config = self.config["network_config"]
        print(f"🌐 路网配置:")
        print(f"   类型: {net_config['type']}")
        if net_config['type'] == 'predefined':
            print(f"   出入口方案: {net_config['entrance_plan']}")
        else:
            print(f"   文件路径: {net_config['file_path']}")
        
        if net_config['road_restriction']['enabled']:
            edges_count = len(net_config['road_restriction']['restricted_edges'])
            print(f"   道路限行: 启用 ({edges_count}个路段)")
        else:
            print(f"   道路限行: 禁用")
        
        # 信号配置
        signal_config = self.config["signal_config"]
        print(f"\n🚦 信号配置:")
        print(f"   类型: {signal_config['type']}")
        if signal_config['type'] == 'custom':
            print(f"   文件路径: {signal_config['file_path']}")
        
        if signal_config['optimization']['enabled']:
            intersections_count = len(signal_config['optimization']['selected_intersections'])
            print(f"   信号优化: 启用 ({intersections_count}个交叉口)")
        else:
            print(f"   信号优化: 禁用")
        
        # 交通配置
        traffic_config = self.config["traffic_config"]
        print(f"\n🚗 交通配置:")
        print(f"   类型: {traffic_config['type']}")
        if traffic_config['type'] == 'predefined':
            print(f"   场景: {traffic_config['scenario']}")
            print(f"   车辆类型: {traffic_config['vehicle_type']}")
            if traffic_config['vip_priority']['enabled']:
                print(f"   VIP优先: 启用")
            else:
                print(f"   VIP优先: 禁用")
        else:
            print(f"   文件路径: {traffic_config['file_path']}")
        
        print("="*50)
    
    def generate_config_file(self, sim_id: str = None):
        """生成配置文件"""
        if sim_id:
            # 保存到指定的仿真目录
            sim_dir = os.path.join("sumo_data", sim_id)
            os.makedirs(sim_dir, exist_ok=True)
            config_filename = os.path.join(sim_dir, f"custom_config_{sim_id}.json")
        else:
            # 保存到当前目录
            config_filename = f"custom_config_{self.get_timestamp()}.json"
        
        with open(config_filename, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 配置已保存到: {config_filename}")
        return config_filename
    
    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%y%m%d_%H%M%S")
    
    def send_to_server(self):
        """发送配置到服务器"""
        print(f"\n🚀 准备发送配置到服务器: {self.server_url}")
        
        # 发送仿真请求
        try:
            print("📤 正在发送仿真请求...")
            response = requests.post(
                f"{self.server_url}/api/start_simulation",
                json=self.config,
                headers={'Content-Type': 'application/json'},
                timeout=300  # 增加到5分钟超时
            )
            
            if response.status_code == 200:
                result = response.json()
                print("🎉 仿真请求发送成功!")
                sim_id = result.get('simulation_id', 'N/A')
                print(f"📋 仿真ID: {sim_id}")
                print(f"💬 消息: {result.get('message', 'N/A')}")
                
                # 显示配置摘要
                config_summary = result.get('config_summary', {})
                if config_summary:
                    print("\n📊 配置摘要:")
                    for key, value in config_summary.items():
                        print(f"   {key}: {value}")
                
                return sim_id
            else:
                print(f"❌ 服务器返回错误: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"🔍 错误详情: {error_info}")
                except:
                    print(f"🔍 错误详情: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 发送请求失败: {e}")
            return False
    
    def run(self):
        """运行交互式配置生成器"""
        self.print_header()
        
        try:
            # 检查路网文件
            if not os.path.exists(self.net_file_path):
                print(f"❌ 路网文件不存在: {self.net_file_path}")
                print("💡 请确保 SUMO 数据文件存在")
                return
            
            print(f"✅ 找到路网文件: {self.net_file_path}")
            
            # 开始配置流程
            self.configure_network()
            self.configure_signal()
            self.configure_traffic()
            
            # 显示配置摘要
            self.display_config_summary()
            
            # 确认配置
            if not self.get_yes_no("\n是否继续执行仿真?"):
                print("❌ 用户取消操作")
                return
            
            # 发送到服务器
            sim_id = self.send_to_server()
            
            if sim_id and sim_id != False:
                # 仿真成功，保存配置文件到仿真目录
                config_file = self.generate_config_file(sim_id)
                
                print("\n🎯 任务完成!")
                print(f"🎮 仿真应该已经在 SUMO-GUI 中启动")
                print(f"📁 配置文件已保存: {config_file}")
                print(f"📊 仿真结果将保存到: sumo_data/{sim_id}/simulation_result_{sim_id}.json")
            else:
                # 仿真失败，保存配置文件到当前目录
                config_file = self.generate_config_file()
                print("\n❌ 发送失败，但配置文件已保存")
                print(f"📁 配置文件: {config_file}")
                print("💡 您可以稍后手动使用此配置文件")
                
        except KeyboardInterrupt:
            print("\n\n⏹️ 用户中断操作")
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    # 检查导入
    try:
        from network_selector.network_selector import IntersectionSelector, EdgeSelector
        print("✅ 网络选择器模块导入成功")
    except ImportError as e:
        print(f"❌ 无法导入网络选择器模块: {e}")
        print("💡 请确保 tools/network_selector 目录存在且包含必要文件")
        return
    
    # 运行交互式生成器
    generator = InteractiveTestGenerator()
    generator.run()

if __name__ == "__main__":
    main() 