# 前后端联调测试指南

## 概述

本指南说明如何进行太行院仿真系统的前后端联调测试，验证网络选择器和仿真接口的正常工作。

## 快速启动

### 方法1：使用启动脚本（推荐）
1. 双击根目录下的 `启动前后端联调.bat`
2. 脚本会自动启动后端API服务器和前端页面

### 方法2：手动启动
1. **启动后端**：
   ```bash
   cd backend
   python api_server.py
   ```
   
2. **打开前端**：
   用浏览器打开 `frontend/index.html`

## 联调验证步骤

### 1. 验证后端API服务器启动
**期望现象**：
- 后端控制台显示服务器启动信息
- 看到端口8888的服务地址信息
- 显示主要接口列表

**如果失败**：
- 检查端口8888是否被占用
- 确认Python环境和依赖包是否正确安装

### 2. 验证前端页面加载
**操作**：
- 在浏览器中打开前端页面
- 检查页面是否正常显示

**期望现象**：
- 页面加载完整，无控制台错误
- 各项配置选项正常显示

### 3. 测试路段选择器（道路限行）
**操作步骤**：
1. 在前端页面勾选"道路限行"选项
2. 观察网络选择器是否自动启动

**期望现象**：
- 前端显示"正在启动路段选择器..."加载提示
- 自动打开新的浏览器窗口/标签页（端口5001）
- 显示路网可视化界面，能看到路段和交叉口
- 可以点击选择路段（高亮显示）
- 点击"确认选择"后窗口关闭
- 前端显示"已选择 X 个限行路段"状态

**如果失败**：
- 检查控制台是否有错误信息
- 确认路网文件 `backend/sumo_data/templates/gym_tls.net.xml` 存在
- 检查端口5001是否被占用

### 4. 测试交叉口选择器（信号优化）
**操作步骤**：
1. 在前端页面勾选"信号灯控制优化"选项
2. 观察网络选择器是否自动启动

**期望现象**：
- 前端显示"正在启动交叉口选择器..."加载提示
- 自动打开新的浏览器窗口/标签页（端口5001）
- 显示路网可视化界面，能看到交通信号灯交叉口
- 可以点击选择交叉口（高亮显示）
- 点击"确认选择"后窗口关闭
- 前端显示"已选择 X 个优化交叉口"状态

### 5. 测试完整仿真流程
**操作步骤**：
1. 配置仿真参数（保持默认即可）
2. 可选：设置道路限行和信号优化
3. 点击"运行仿真"按钮

**期望现象**：
- 前端显示"正在验证配置..."
- 然后显示"正在启动仿真..."
- 仿真完成后显示"仿真执行成功!"
- 控制台输出仿真结果数据

## 接口测试详情

### API基础信息
- **后端地址**：`http://localhost:8888`
- **数据格式**：JSON
- **跨域**：已启用CORS

### 主要接口
1. **配置验证**：`POST /api/config/validate`
2. **网络选择器**：`POST /api/network/select`
3. **仿真启动**：`POST /api/start_simulation`

### 网络选择器接口详情
**端点**：`POST /api/network/select`

**请求格式**：
```json
{
  "net_file_path": "sumo_data/templates/gym_tls.net.xml",
  "selector_type": "edge"  // 或 "intersection"
}
```

**响应格式**：
```json
{
  "success": true,
  "selected_ids": ["10005414142", "10309271185"],
  "selector_type": "edge",
  "count": 2
}
```

## 常见问题排查

### 问题1：后端启动失败
**可能原因**：
- 端口8888被占用
- Python环境问题
- 缺少依赖包

**解决方案**：
```bash
# 检查端口占用
netstat -ano | findstr :8888

# 安装依赖
pip install flask flask-cors

# 检查Python版本
python --version
```

### 问题2：网络选择器无法启动
**可能原因**：
- 端口5001被占用
- 路网文件路径错误
- 网络选择器模块导入失败

**解决方案**：
```bash
# 检查端口占用
netstat -ano | findstr :5001

# 验证路网文件
python -c "import os; print(os.path.exists('backend/sumo_data/templates/gym_tls.net.xml'))"

# 测试网络选择器
cd backend
python -c "from tools.network_selector.network_selector import EdgeSelector; print('导入成功')"
```

### 问题3：前端无法连接后端
**可能原因**：
- 后端服务器未启动
- 端口配置错误
- 防火墙阻拦

**解决方案**：
1. 确认后端服务器正在运行
2. 检查前端中的API_BASE配置
3. 在浏览器中直接访问 `http://localhost:8888`

### 问题4：选择器界面空白
**可能原因**：
- 路网文件解析失败
- 静态文件路径错误
- 浏览器兼容性问题

**解决方案**：
1. 检查后端控制台的解析信息
2. 确认 `backend/tools/network_selector/static/` 和 `templates/` 目录存在
3. 尝试使用Chrome或Edge浏览器

## 成功标准

联调测试成功的标准：
- ✅ 后端API服务器正常启动
- ✅ 前端页面正常加载
- ✅ 路段选择器能正常启动和选择
- ✅ 交叉口选择器能正常启动和选择
- ✅ 完整仿真流程能正常执行
- ✅ 前后端数据交互正常
- ✅ 错误处理机制正常工作
