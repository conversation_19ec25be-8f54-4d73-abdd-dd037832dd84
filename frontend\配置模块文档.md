# 仿真配置JSON接口文档

## 概述
本文档定义了新版前端界面与后端仿真系统交互的JSON配置格式。配置分为三个主要模块：仿真路网、信号配时、交通需求。

## 接口信息
- **接口路径**: `/api/start_simulation`
- **请求方法**: `POST`
- **Content-Type**: `application/json`
- **编码**: `UTF-8`

---

## JSON配置结构

### 完整示例
```json
{
  "network_config": {
    "type": "predefined",
    "file_path": null,
    "entrance_plan": "仅开放东侧出入口",
    "road_restriction": {
      "enabled": true,
      "restricted_edges": ["edge_123", "edge_456", "edge_789"]
    }
  },
  "signal_config": {
    "type": "predefined",
    "file_path": null,
    "optimization": {
      "enabled": true,
      "selected_intersections": ["10005414142", "10007624522", "10309271185"]
    }
  },
  "traffic_config": {
    "type": "predefined",
    "file_path": null,
    "scenario": "进场",
    "vehicle_type": "仅一般车辆",
    "vip_priority": {
      "enabled": false
    }
  }
}
```

---

## 配置模块详细说明

### 1. 仿真路网配置 (`network_config`)

#### `type` (必填)
- **类型**: `string`
- **描述**: 路网配置类型
- **可选值**:
  - `"predefined"`: 使用预设路网
  - `"custom"`: 使用自定义路网文件
- **示例**: `"predefined"`

#### `file_path` (条件必填)
- **类型**: `string` 或 `null`
- **描述**: 自定义路网文件路径 (仅*.net.xml)
- **适用条件**: 当 `type` 为 `"custom"` 时必填，为 `"predefined"` 时为 `null`
- **示例**: `"/uploads/custom_network.net.xml"` 或 `null`

#### `entrance_plan` (条件必填)
- **类型**: `string`
- **描述**: 出入口方案 (仅预设路网时有效)
- **适用条件**: 当 `type` 为 `"predefined"` 时必填
- **可选值**:
  - `"仅开放东侧出入口"`
  - `"仅开放南侧出入口"`
  - `"全部开放"`
- **示例**: `"仅开放东侧出入口"`

#### `road_restriction` (可选)
- **类型**: `object`
- **描述**: 道路限行配置

##### `road_restriction.enabled` (必填)
- **类型**: `boolean`
- **描述**: 是否启用道路限行
- **示例**: `true`

##### `road_restriction.restricted_edges` (条件必填)
- **类型**: `array[string]`
- **描述**: 限行路段ID列表 (用户点选的路段)
- **适用条件**: 当 `enabled` 为 `true` 时必填
- **示例**: `["edge_123", "edge_456", "edge_789"]`

### 2. 信号配时配置 (`signal_config`)

#### `type` (必填)
- **类型**: `string`
- **描述**: 信号配时类型
- **可选值**:
  - `"predefined"`: 使用预设信号配时
  - `"custom"`: 使用自定义信号配时文件
- **示例**: `"predefined"`

#### `file_path` (条件必填)
- **类型**: `string` 或 `null`
- **描述**: 自定义信号配时文件路径 (仅*.add.xml)
- **适用条件**: 当 `type` 为 `"custom"` 时必填，为 `"predefined"` 时为 `null`
- **示例**: `"/uploads/custom_signals.add.xml"` 或 `null`

#### `optimization` (可选)
- **类型**: `object`
- **描述**: 信号优化配置

##### `optimization.enabled` (必填)
- **类型**: `boolean`
- **描述**: 是否启用信号优化
- **示例**: `true`

##### `optimization.selected_intersections` (条件必填)
- **类型**: `array[string]`
- **描述**: 用户选择的待优化交叉口ID列表
- **适用条件**: 当 `enabled` 为 `true` 时必填
- **示例**: `["10005414142", "10007624522", "10309271185"]`

### 3. 交通需求配置 (`traffic_config`)

#### `type` (必填)
- **类型**: `string`
- **描述**: 交通需求类型
- **可选值**:
  - `"predefined"`: 使用预设交通需求
  - `"custom"`: 使用自定义交通需求文件
- **示例**: `"predefined"`

#### `file_path` (条件必填)
- **类型**: `string` 或 `null`
- **描述**: 自定义交通需求文件路径 (仅*.rou.xml)
- **适用条件**: 当 `type` 为 `"custom"` 时必填，为 `"predefined"` 时为 `null`
- **示例**: `"/uploads/custom_routes.rou.xml"` 或 `null`

#### `scenario` (条件必填)
- **类型**: `string`
- **描述**: 组织时段 (仅预设需求时有效)
- **适用条件**: 当 `type` 为 `"predefined"` 时必填
- **可选值**:
  - `"进场"`
  - `"离场"`
- **示例**: `"进场"`

#### `vehicle_type` (条件必填)
- **类型**: `string`
- **描述**: 车辆类型 (仅预设需求时有效)
- **适用条件**: 当 `type` 为 `"predefined"` 时必填
- **可选值**:
  - `"仅一般车辆"`
  - `"存在贵宾专车"`
- **示例**: `"仅一般车辆"`

#### `vip_priority` (可选)
- **类型**: `object`
- **描述**: 贵宾专车优先通行配置

##### `vip_priority.enabled` (必填)
- **类型**: `boolean`
- **描述**: 是否启用贵宾专车优先通行
- **适用条件**: 仅当 `vehicle_type` 为 `"存在贵宾专车"` 时可以为 `true`
- **示例**: `false`

---

## 配置场景示例

### 场景1: 全预设配置
```json
{
  "network_config": {
    "type": "predefined",
    "file_path": null,
    "entrance_plan": "全部开放",
    "road_restriction": {
      "enabled": false,
      "restricted_edges": []
    }
  },
  "signal_config": {
    "type": "predefined",
    "file_path": null,
    "optimization": {
      "enabled": false,
      "selected_intersections": []
    }
  },
  "traffic_config": {
    "type": "predefined",
    "file_path": null,
    "scenario": "进场",
    "vehicle_type": "仅一般车辆",
    "vip_priority": {
      "enabled": false
    }
  }
}
```

### 场景2: 预设路网 + 自定义优化
```json
{
  "network_config": {
    "type": "predefined",
    "file_path": null,
    "entrance_plan": "仅开放东侧出入口",
    "road_restriction": {
      "enabled": true,
      "restricted_edges": ["1225366619#0", "980398775#2", "1092566429#10"]
    }
  },
  "signal_config": {
    "type": "predefined",
    "file_path": null,
    "optimization": {
      "enabled": true,
      "selected_intersections": ["10005414142", "10007624522", "10309271185", "10309271336"]
    }
  },
  "traffic_config": {
    "type": "predefined",
    "file_path": null,
    "scenario": "离场",
    "vehicle_type": "存在贵宾专车",
    "vip_priority": {
      "enabled": true
    }
  }
}
```

### 场景3: 完全自定义配置
```json
{
  "network_config": {
    "type": "custom",
    "file_path": "/uploads/user123/custom_network.net.xml",
    "entrance_plan": null,
    "road_restriction": {
      "enabled": false,
      "restricted_edges": []
    }
  },
  "signal_config": {
    "type": "custom",
    "file_path": "/uploads/user123/custom_signals.add.xml",
    "optimization": {
      "enabled": false,
      "selected_intersections": []
    }
  },
  "traffic_config": {
    "type": "custom",
    "file_path": "/uploads/user123/custom_routes.rou.xml",
    "scenario": null,
    "vehicle_type": null,
    "vip_priority": {
      "enabled": false
    }
  }
}
```

---

## 配置规则与约束

### 文件类型约束
- `network_config.file_path`: 仅接受 `*.net.xml` 格式
- `signal_config.file_path`: 仅接受 `*.add.xml` 格式  
- `traffic_config.file_path`: 仅接受 `*.rou.xml` 格式

### 业务逻辑约束
- VIP优先通行 (`vip_priority.enabled: true`) 仅当 `vehicle_type` 为 `"存在贵宾专车"` 时有效
- 道路限行需要用户通过可视化界面点选具体路段
- 信号优化需要用户通过可视化界面点选具体交叉口

### 条件字段规则
- `type` 为 `"predefined"` 时，对应的 `file_path` 必须为 `null`
- `type` 为 `"custom"` 时，对应的 `file_path` 必须提供有效路径
- 预设配置时，相关预设字段必填；自定义配置时，预设字段为 `null`

---

## 响应格式

### 成功响应
```json
{
  "success": true,
  "simulation_id": "250628143022",
  "message": "仿真配置已接收，正在启动仿真",
  "config_summary": {
    "network": "预设路网 - 仅开放东侧出入口",
    "signal": "预设配时 + 自定义优化(4个交叉口)",
    "traffic": "预设需求 - 进场场景 + 贵宾专车"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error_code": "INVALID_CONFIG",
  "message": "配置验证失败",
  "details": {
    "module": "traffic_config",
    "field": "vip_priority.enabled",
    "error": "VIP优先通行需要车辆类型为'存在贵宾专车'"
  }
}
```
