:root {
    --primary-color: #8DD2DB;
    --primary-light: #1E546A;
    --secondary-color: #34a853;
    --text-color: #202124;
    --light-gray: #f1f3f4;
    --border-color: #dadce0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --card-radius: 6px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

body {
    background-color: #f8f9fa;
    color: var(--text-color);
    line-height: 1.4;
}

.container {
    max-width: 98%;
    margin: 15px auto;
    padding: 0 10px;
}

header {
    text-align: center;
    margin-bottom: 15px;
}

header h1 {
    font-size: 24px;
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

header h1:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 2px;
}

.card {
    background: white;
    border-radius: var(--card-radius);
    box-shadow: 0 1px 5px var(--shadow-color);
    padding: 12px 15px;
    margin-bottom: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px var(--shadow-color);
}

h2 {
    color: var(--primary-color);
    font-size: 16px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}

.option-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.option-group.compact {
    gap: 8px;
    margin-bottom: 5px;
}

.option {
    display: flex;
    align-items: center;
    gap: 5px;
}

.option input[type="radio"] {
    appearance: none;
    width: 14px;
    height: 14px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
}

.option input[type="radio"]:checked {
    border-color: var(--primary-color);
}

.option input[type="radio"]:checked::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 7px;
    height: 7px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.option label {
    font-size: 14px;
    cursor: pointer;
}

.upload-btn {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 3px;
    padding: 4px 10px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
    margin-left: auto;
    white-space: nowrap;
    min-width: 150px;
    justify-content: center;
}

.upload-btn:hover {
    background-color: #29718d;
}

.sub-options {
    margin-top: 8px;
}

.sub-option {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 8px;
    gap: 6px;
}

.sub-option.indent {
    margin-left: 15px;
}

.bullet {
    font-size: 16px;
    color: var(--primary-color);
    line-height: 1;
}

.select-wrapper {
    position: relative;
    margin-right: 8px;
}

.select-wrapper:after {
    content: '▼';
    font-size: 10px;
    color: var(--primary-color);
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

select {
    appearance: none;
    padding: 5px 25px 5px 8px;
    border-radius: 3px;
    border: 1px solid var(--border-color);
    background: #1E546A;
    color: #95D7E3;
    font-size: 13px;
    cursor: pointer;
    outline: none;
    min-width: 160px;
}

select option:hover {
    background: #CEB534;
}


select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
}

input[type="checkbox"] {
    appearance: none;
    width: 14px;
    height: 14px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    position: relative;
    margin-right: 6px;
}

input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 11px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.note {
    color: #5f6368;
    font-size: 11px;
    font-style: italic;
}

.run-btn {
    display: block;
    margin: 15px auto;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 3px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.run-btn:hover {
    background-color: #2d9348;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.run-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 组织方案网格布局 */
.org-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10px;
}

.org-grid .sub-option {
    margin-bottom: 5px;
}

/* 禁用状态样式 */
.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.disabled select,
.disabled input,
.disabled button {
    pointer-events: none;
}

select:disabled, 
input:disabled {
    background-color: var(--light-gray);
    border-color: var(--border-color);
    color: #999;
    cursor: not-allowed;
}

.option-group:has(input[type="radio"]:disabled) {
    opacity: 0.7;
}

/* 使用网格布局使页面更紧凑 */
@media (min-width: 768px) {
    main {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 12px;
    }
    
    #organizationSection {
        grid-column: 1 / 3;
    }
    
    .run-btn {
        grid-column: 1 / 3;
    }
}

@media (max-width: 767px) {
    .container {
        padding: 0 8px;
    }
    
    .option-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .upload-btn {
        margin-left: 0;
        width: 100%;
        justify-content: center;
    }
    
    .sub-option {
        flex-direction: column;
        align-items: flex-start;
    }
    
    select {
        width: 100%;
    }
    
    .org-grid {
        grid-template-columns: 1fr;
    }
} 