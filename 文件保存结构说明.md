# 文件保存结构说明

## 概述

修复后的系统文件保存结构清晰明确，避免了重复保存和文件散乱的问题。

## 文件保存结构

### 1. 仿真成功时的文件结构

```
sumo_data/
├── {sim_id}/                                    # 仿真专用目录
│   ├── custom_config_{sim_id}.json             # 用户配置文件
│   ├── simulation_result_{sim_id}.json         # 仿真结果文件
│   ├── routes.rou.xml                          # 生成的路由文件
│   ├── background.rou.xml                      # 背景交通流
│   ├── gym_vehicle.rou.xml                     # 活动车辆流
│   ├── gym_people.rou.xml                      # 活动人员流
│   ├── restricted_network.net.xml              # 限行路网（如果启用）
│   ├── optimized_signal.add.xml                # 优化信号（如果启用）
│   ├── summary.xml                             # SUMO仿真摘要
│   ├── person.summary.xml                      # 行人摘要
│   ├── tripinfo.xml                            # 行程信息
│   ├── statistics.xml                          # 统计信息
│   └── weight/                                 # 权重文件夹
│       ├── weight_vehicle.src.xml              # 车辆源权重
│       ├── weight_vehicle.dst.xml              # 车辆目标权重
│       ├── weight_pedestrian.src.xml           # 行人源权重
│       └── weight_pedestrian.dst.xml           # 行人目标权重
```

### 2. 仿真失败时的文件结构

```
项目根目录/
├── custom_config_{timestamp}.json              # 备份配置文件
└── sumo_data/
    └── templates/                              # 模板文件（不变）
```

## 文件说明

### 配置文件 (`custom_config_{sim_id}.json`)

**位置**: `sumo_data/{sim_id}/custom_config_{sim_id}.json`

**内容**: 用户的完整配置信息

```json
{
  "network_config": {
    "type": "predefined",
    "file_path": null,
    "entrance_plan": "仅开放东侧出入口",
    "road_restriction": {
      "enabled": true,
      "restricted_edges": ["-980398780#2", "980398780#2"]
    }
  },
  "signal_config": {
    "type": "predefined",
    "file_path": null,
    "optimization": {
      "enabled": true,
      "selected_intersections": ["10005414142", "10007624522"]
    }
  },
  "traffic_config": {
    "type": "predefined",
    "file_path": null,
    "scenario": "进场",
    "vehicle_type": "存在贵宾专车",
    "vip_priority": {
      "enabled": true
    }
  }
}
```

### 仿真结果文件 (`simulation_result_{sim_id}.json`)

**位置**: `sumo_data/{sim_id}/simulation_result_{sim_id}.json`

**内容**: 仿真执行结果和性能指标

```json
{
  "simulation_id": "250629174456",
  "config": {
    "network_config": {
      "type": "predefined",
      "file_path": null,
      "entrance_plan": "仅开放东侧出入口",
      "road_restriction": {
        "enabled": true,
        "restricted_edges": ["edge_123", "edge_456"]
      }
    },
    "signal_config": {
      "type": "predefined",
      "file_path": null,
      "optimization": {
        "enabled": true,
        "selected_intersections": ["10005414142", "10007624522"]
      }
    },
    "traffic_config": {
      "type": "predefined",
      "file_path": null,
      "scenario": "进场",
      "vehicle_type": "存在贵宾专车",
      "vip_priority": {
        "enabled": true
      }
    }
  },
  "simulation_results": {
    "pedestrian_metrics": {
      "average_travel_time": 123.45,
      "average_waiting_time": 12.34,
      "average_waiting_count": 2.1,
      "average_time_loss": 23.45
    },
    "vehicle_metrics": {
      "average_travel_time": 234.56,
      "average_waiting_time": 45.67,
      "average_waiting_count": 3.2,
      "average_time_loss": 67.89
    },
    "vip_vehicle_metrics": {
      "average_travel_time": 145.23,
      "average_waiting_time": 8.91,
      "average_waiting_count": 1.2,
      "average_time_loss": 12.34
    },
    "venue_area_metrics": {
      "average_pedestrian_travel_time": 156.78,
      "average_pedestrian_delay": 34.56,
      "average_vehicle_travel_time": 267.89,
      "average_vehicle_delay": 78.90
    }
  }
}
```

## 文件保存逻辑

### 1. 交互式脚本 (`interactive_test.py`)

```python
def generate_config_file(self, sim_id: str = None):
    """生成配置文件"""
    if sim_id:
        # 保存到指定的仿真目录
        sim_dir = os.path.join("sumo_data", sim_id)
        os.makedirs(sim_dir, exist_ok=True)
        config_filename = os.path.join(sim_dir, f"custom_config_{sim_id}.json")
    else:
        # 保存到当前目录作为备份
        config_filename = f"custom_config_{self.get_timestamp()}.json"
    
    with open(config_filename, 'w', encoding='utf-8') as f:
        json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 配置已保存到: {config_filename}")
    return config_filename
```

### 2. API服务器 (`api_server.py`)

```python
# 保存详细结果到仿真目录
sim_dir = os.path.join('sumo_data', sim_id)
os.makedirs(sim_dir, exist_ok=True)
result_file = os.path.join(sim_dir, f'simulation_result_{sim_id}.json')
with open(result_file, 'w', encoding='utf-8') as f:
    json.dump(result_data, f, ensure_ascii=False, indent=4)
```