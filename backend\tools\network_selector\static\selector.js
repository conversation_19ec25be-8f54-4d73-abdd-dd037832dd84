class NetworkSelector {
    constructor() {
        // 获取配置
        this.config = window.SELECTOR_CONFIG || {
            selector_type: 'mixed',
            show_intersections: true,
            show_edges: true,
            allow_intersection_selection: true,
            allow_edge_selection: true,
            primary_object: 'mixed'
        };
        
        this.canvas = document.getElementById('networkCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.intersections = [];
        this.edges = [];
        this.selectedIntersectionIds = new Set();
        this.selectedEdgeIds = new Set();
        
        // 缩放和平移相关
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.baseScale = 1;
        this.baseOffsetX = 0;
        this.baseOffsetY = 0;
        
        // 拖拽相关
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.dragStartOffsetX = 0;
        this.dragStartOffsetY = 0;
        
        // 显示选项
        this.showEdges = this.config.show_edges;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadNetworkData();
    }

    initializeElements() {
        this.selectAllBtn = document.getElementById('selectAll');
        this.clearAllBtn = document.getElementById('clearAll');
        this.confirmBtn = document.getElementById('confirmBtn');
        this.zoomInBtn = document.getElementById('zoomIn');
        this.zoomOutBtn = document.getElementById('zoomOut');
        this.zoomResetBtn = document.getElementById('zoomReset');
        this.totalCountSpan = document.getElementById('totalCount');
        this.selectedCountSpan = document.getElementById('selectedCount');
        this.selectedList = document.getElementById('selectedList');
        this.status = document.getElementById('status');
        this.tooltip = document.getElementById('tooltip');
        this.loading = document.getElementById('loading');
    }

    setupEventListeners() {
        // 按钮事件
        this.selectAllBtn.addEventListener('click', () => this.selectAll());
        this.clearAllBtn.addEventListener('click', () => this.clearAll());
        this.confirmBtn.addEventListener('click', () => this.confirmSelection());

        // 缩放按钮事件
        this.zoomInBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.zoomAtCenter(1.2);
        });
        
        this.zoomOutBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.zoomAtCenter(0.8);
        });
        
        this.zoomResetBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.resetZoom();
        });

        // Canvas事件
        this.canvas.addEventListener('mousedown', (e) => {
            e.preventDefault();
            this.handleMouseDown(e);
        });
        
        this.canvas.addEventListener('mousemove', (e) => {
            e.preventDefault();
            this.handleMouseMove(e);
        });
        
        this.canvas.addEventListener('mouseup', (e) => {
            e.preventDefault();
            this.handleMouseUp(e);
        });
        
        // 全局监听，防止拖拽时鼠标离开canvas
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                this.handleGlobalMouseMove(e);
            }
        });
        
        document.addEventListener('mouseup', (e) => {
            if (this.isDragging) {
                this.handleMouseUp(e);
            }
        });
        
        this.canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            this.handleWheel(e);
        }, { passive: false });
        
        this.canvas.addEventListener('mouseleave', () => {
            this.tooltip.style.display = 'none';
        });

        // 窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(() => this.resizeCanvas(), 100);
        });
        
        // 防止右键菜单
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());

        // 显示控制
        const showEdgesCheckbox = document.getElementById('showEdges');
        if (showEdgesCheckbox) {
            showEdgesCheckbox.addEventListener('change', (e) => {
                this.showEdges = e.target.checked;
                this.drawNetwork();
            });
        }
    }

    async loadNetworkData() {
        try {
            console.log('开始加载路网数据...');
            const response = await fetch('/api/network_data');
            const data = await response.json();
            
            this.intersections = data.intersections;
            this.edges = data.edges || [];
            this.bounds = data.bounds;
            
            // 更新配置为从后端获取的配置
            if (data.config) {
                this.config = { ...this.config, ...data.config };
                console.log('更新后的配置:', this.config);
            }
            
            console.log(`加载了 ${data.intersection_count} 个交叉口, ${data.edge_count} 条路段`);
            console.log('边界:', this.bounds);
            console.log('配置:', data.config);
            
            // 更新UI显示
            if (this.config.selector_type === 'intersection') {
                this.totalCountSpan.textContent = `交叉口: ${data.intersection_count}`;
            } else if (this.config.selector_type === 'edge') {
                this.totalCountSpan.textContent = `路段: ${data.edge_count}`;
            } else {
                this.totalCountSpan.textContent = `交叉口: ${data.intersection_count} | 路段: ${data.edge_count}`;
            }
            
            this.loading.style.display = 'none';
            
            this.resizeCanvas();
            this.fitToCanvas();
            this.drawNetwork();
            
        } catch (error) {
            console.error('加载失败:', error);
            this.showStatus('加载路网数据失败: ' + error.message, 'error');
            this.loading.textContent = '加载失败';
        }
    }

    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        
        console.log(`Canvas 尺寸: ${this.canvas.width} x ${this.canvas.height}`);
        
        if (this.intersections.length > 0) {
            this.fitToCanvas();
        }
        this.drawNetwork();
    }

    fitToCanvas() {
        if (!this.bounds || Object.keys(this.bounds).length === 0) return;

        const margin = 50;
        const canvasWidth = this.canvas.width - 2 * margin;
        const canvasHeight = this.canvas.height - 2 * margin;
        
        const dataWidth = this.bounds.max_x - this.bounds.min_x;
        const dataHeight = this.bounds.max_y - this.bounds.min_y;
        
        if (dataWidth === 0 || dataHeight === 0) return;
        
        this.baseScale = Math.min(canvasWidth / dataWidth, canvasHeight / dataHeight);
        
        const dataCenterX = (this.bounds.min_x + this.bounds.max_x) / 2;
        const dataCenterY = (this.bounds.min_y + this.bounds.max_y) / 2;
        
        const canvasCenterX = this.canvas.width / 2;
        const canvasCenterY = this.canvas.height / 2;
        
        this.baseOffsetX = canvasCenterX - dataCenterX * this.baseScale;
        this.baseOffsetY = canvasCenterY + dataCenterY * this.baseScale;
        
        this.resetZoom();
    }

    worldToScreen(worldX, worldY) {
        const screenX = worldX * this.scale + this.offsetX;
        const screenY = -worldY * this.scale + this.offsetY;
        return { x: screenX, y: screenY };
    }

    screenToWorld(screenX, screenY) {
        const worldX = (screenX - this.offsetX) / this.scale;
        const worldY = -(screenY - this.offsetY) / this.scale;
        return { x: worldX, y: worldY };
    }

    zoomAtCenter(factor) {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        const worldPos = this.screenToWorld(centerX, centerY);
        
        this.scale *= factor;
        
        const newScreenPos = this.worldToScreen(worldPos.x, worldPos.y);
        this.offsetX += centerX - newScreenPos.x;
        this.offsetY += centerY - newScreenPos.y;
        
        this.drawNetwork();
    }

    resetZoom() {
        this.scale = this.baseScale;
        this.offsetX = this.baseOffsetX;
        this.offsetY = this.baseOffsetY;
        this.drawNetwork();
    }

    getMousePos(event) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
    }

    handleMouseDown(event) {
        const pos = this.getMousePos(event);
        
        if (event.button === 0) { // 左键
            // 根据配置决定是否检查交叉口
            if (this.config.allow_intersection_selection) {
                const intersection = this.getIntersectionAt(pos.x, pos.y);
                if (intersection) {
                    console.log(`点击交叉口: ${intersection.id}`);
                    this.toggleIntersectionSelection(intersection.id);
                    return;
                }
            }
            
            // 根据配置决定是否检查路段
            if (this.config.allow_edge_selection) {
                const edge = this.getEdgeAt(pos.x, pos.y);
                if (edge) {
                    console.log(`点击路段: ${edge.id}`);
                    this.toggleEdgeSelection(edge.id);
                    return;
                }
            }
            
            // 开始拖拽
            this.isDragging = true;
            this.dragStartX = pos.x;
            this.dragStartY = pos.y;
            this.dragStartOffsetX = this.offsetX;
            this.dragStartOffsetY = this.offsetY;
            this.canvas.style.cursor = 'grabbing';
        }
    }

    handleMouseMove(event) {
        const pos = this.getMousePos(event);
        
        if (this.isDragging) {
            const deltaX = pos.x - this.dragStartX;
            const deltaY = pos.y - this.dragStartY;
            
            this.offsetX = this.dragStartOffsetX + deltaX;
            this.offsetY = this.dragStartOffsetY + deltaY;
            
            this.drawNetwork();
        } else {
            // 显示提示
            let tooltipText = '';
            let hasHover = false;
            
            // 只有在允许选择交叉口且不隐藏交叉口标签时才显示交叉口tooltip
            if (this.config.allow_intersection_selection && !this.config.hide_intersection_labels) {
                const intersection = this.getIntersectionAt(pos.x, pos.y);
                if (intersection) {
                    tooltipText = `交叉口: ${intersection.id}`;
                    hasHover = true;
                }
            }
            
            if (!hasHover && this.config.allow_edge_selection) {
                const edge = this.getEdgeAt(pos.x, pos.y);
                if (edge) {
                    // 优先显示路段名称，如果有名称则显示"名称 (ID)"，否则只显示ID
                    if (edge.name && edge.name.trim()) {
                        tooltipText = `路段: ${edge.name} (${edge.id})`;
                    } else {
                        tooltipText = `路段: ${edge.id}`;
                    }
                    hasHover = true;
                }
            }
            
            if (hasHover) {
                this.tooltip.textContent = tooltipText;
                this.tooltip.style.left = (event.clientX + 10) + 'px';
                this.tooltip.style.top = (event.clientY - 30) + 'px';
                this.tooltip.style.display = 'block';
                this.canvas.style.cursor = 'pointer';
            } else {
                this.tooltip.style.display = 'none';
                this.canvas.style.cursor = 'grab';
            }
        }
    }

    handleGlobalMouseMove(event) {
        if (!this.isDragging) return;
        
        const pos = this.getMousePos(event);
        const deltaX = pos.x - this.dragStartX;
        const deltaY = pos.y - this.dragStartY;
        
        this.offsetX = this.dragStartOffsetX + deltaX;
        this.offsetY = this.dragStartOffsetY + deltaY;
        
        this.drawNetwork();
    }

    handleMouseUp(event) {
        if (this.isDragging) {
            this.isDragging = false;
            this.canvas.style.cursor = 'grab';
        }
    }

    handleWheel(event) {
        const pos = this.getMousePos(event);
        const worldPos = this.screenToWorld(pos.x, pos.y);
        
        const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
        this.scale *= zoomFactor;
        
        const newScreenPos = this.worldToScreen(worldPos.x, worldPos.y);
        this.offsetX += pos.x - newScreenPos.x;
        this.offsetY += pos.y - newScreenPos.y;
        
        this.drawNetwork();
    }

    drawNetwork() {
        if (!this.ctx) return;

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 根据配置绘制路段
        if (this.config.show_edges && this.showEdges && this.edges.length > 0) {
            this.drawEdges();
        }

        // 根据配置绘制交叉口
        if (this.config.show_intersections && this.intersections.length > 0) {
            this.drawIntersections();
        }
        
        this.drawStatusInfo();
    }

    drawEdges() {
        const relativeScale = this.scale / this.baseScale;
        let visibleEdges = 0;

        this.edges.forEach(edge => {
            if (edge.points.length < 2) return;

            // 检查路段是否在可视区域内
            let inView = false;
            for (const point of edge.points) {
                const screenPos = this.worldToScreen(point.x, point.y);
                
                if (screenPos.x >= -100 && screenPos.x <= this.canvas.width + 100 && 
                    screenPos.y >= -100 && screenPos.y <= this.canvas.height + 100) {
                    inView = true;
                    break;
                }
            }
            
            if (!inView) return;
            visibleEdges++;

            // 根据选择状态设置样式
            const isSelected = this.selectedEdgeIds.has(edge.id);
            const lineWidth = Math.max(2, Math.min(6, (isSelected ? 4 : 2) * Math.sqrt(relativeScale)));
            
            this.ctx.strokeStyle = isSelected ? '#e74c3c' : '#bdc3c7';
            this.ctx.lineWidth = lineWidth;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';

            this.ctx.beginPath();
            
            const firstPoint = edge.points[0];
            const startPos = this.worldToScreen(firstPoint.x, firstPoint.y);
            this.ctx.moveTo(startPos.x, startPos.y);
            
            for (let i = 1; i < edge.points.length; i++) {
                const point = edge.points[i];
                const screenPos = this.worldToScreen(point.x, point.y);
                this.ctx.lineTo(screenPos.x, screenPos.y);
            }
            
            this.ctx.stroke();

            // 显示路段ID
            if (relativeScale > 1.0 && edge.points.length >= 2) {
                const midIndex = Math.floor(edge.points.length / 2);
                const midPoint = edge.points[midIndex];
                const textPos = this.worldToScreen(midPoint.x, midPoint.y);
                
                this.ctx.fillStyle = isSelected ? '#c0392b' : 'rgba(108, 117, 125, 0.8)';
                this.ctx.font = `${Math.max(8, Math.min(12, 9 * Math.sqrt(relativeScale)))}px Arial`;
                this.ctx.textAlign = 'center';
                
                // 优先显示路段名称，如果没有名称则显示ID
                const displayText = edge.name && edge.name.trim() ? edge.name : edge.id;
                
                const textWidth = this.ctx.measureText(displayText).width;
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                this.ctx.fillRect(textPos.x - textWidth/2 - 2, textPos.y - 8, textWidth + 4, 12);
                
                this.ctx.fillStyle = isSelected ? '#c0392b' : 'rgba(108, 117, 125, 0.8)';
                this.ctx.fillText(displayText, textPos.x, textPos.y);
            }
        });
    }

    drawIntersections() {
        let visibleIntersections = 0;
        
        this.intersections.forEach(intersection => {
            const screenPos = this.worldToScreen(intersection.x, intersection.y);
            
            if (screenPos.x < -30 || screenPos.x > this.canvas.width + 30 || 
                screenPos.y < -30 || screenPos.y > this.canvas.height + 30) {
                return;
            }
            
            visibleIntersections++;
            
            const radius = Math.max(5, Math.min(18, 10 * Math.sqrt(this.scale / this.baseScale)));

            // 绘制白色边框
            this.ctx.beginPath();
            this.ctx.arc(screenPos.x, screenPos.y, radius + 2, 0, 2 * Math.PI);
            this.ctx.fillStyle = 'white';
            this.ctx.fill();
            
            // 绘制交叉口
            this.ctx.beginPath();
            this.ctx.arc(screenPos.x, screenPos.y, radius, 0, 2 * Math.PI);
            
            if (this.selectedIntersectionIds.has(intersection.id)) {
                this.ctx.fillStyle = '#e74c3c';
                this.ctx.strokeStyle = '#c0392b';
            } else {
                this.ctx.fillStyle = '#3498db';
                this.ctx.strokeStyle = '#2980b9';
            }
            
            this.ctx.lineWidth = 2;
            this.ctx.fill();
            this.ctx.stroke();

            // 绘制ID标签
            const relativeScale = this.scale / this.baseScale;
            if (relativeScale > 0.3 && !this.config.hide_intersection_labels) {
                this.ctx.fillStyle = '#2c3e50';
                this.ctx.font = `bold ${Math.max(9, Math.min(16, 11 * Math.sqrt(relativeScale)))}px Arial`;
                this.ctx.textAlign = 'center';
                this.ctx.strokeStyle = 'white';
                this.ctx.lineWidth = 3;
                this.ctx.strokeText(intersection.id, screenPos.x, screenPos.y - radius - 5);
                this.ctx.fillText(intersection.id, screenPos.x, screenPos.y - radius - 5);
            }
        });
    }

    drawStatusInfo() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';
        const zoomPercent = Math.round((this.scale / this.baseScale) * 100);
        
        let statusText = `缩放: ${zoomPercent}%`;
        if (this.config.show_intersections) {
            statusText += ` | 交叉口: ${this.intersections.length}`;
        }
        if (this.config.show_edges) {
            statusText += ` | 路段: ${this.edges.length}`;
        }
        
        this.ctx.fillText(statusText, 10, this.canvas.height - 10);
    }

    getIntersectionAt(mouseX, mouseY) {
        const tolerance = 20;
        let closestIntersection = null;
        let closestDistance = Infinity;
        
        for (const intersection of this.intersections) {
            const screenPos = this.worldToScreen(intersection.x, intersection.y);
            
            const distance = Math.sqrt((mouseX - screenPos.x) ** 2 + (mouseY - screenPos.y) ** 2);
            if (distance <= tolerance && distance < closestDistance) {
                closestDistance = distance;
                closestIntersection = intersection;
            }
        }
        
        return closestIntersection;
    }

    toggleIntersectionSelection(intersectionId) {
        if (this.selectedIntersectionIds.has(intersectionId)) {
            this.selectedIntersectionIds.delete(intersectionId);
        } else {
            this.selectedIntersectionIds.add(intersectionId);
        }
        
        this.updateSelectedCount();
        this.updateSelectedList();
        this.drawNetwork();
        this.updateConfirmButton();
    }

    toggleEdgeSelection(edgeId) {
        if (this.selectedEdgeIds.has(edgeId)) {
            this.selectedEdgeIds.delete(edgeId);
        } else {
            this.selectedEdgeIds.add(edgeId);
        }
        
        this.updateSelectedCount();
        this.updateSelectedList();
        this.drawNetwork();
        this.updateConfirmButton();
    }

    selectAll() {
        this.selectedIntersectionIds.clear();
        this.selectedEdgeIds.clear();
        
        if (this.config.allow_intersection_selection) {
            this.intersections.forEach(intersection => {
                this.selectedIntersectionIds.add(intersection.id);
            });
        }
        
        if (this.config.allow_edge_selection) {
            this.edges.forEach(edge => {
                this.selectedEdgeIds.add(edge.id);
            });
        }
        
        this.updateSelectedCount();
        this.updateSelectedList();
        this.drawNetwork();
        this.updateConfirmButton();
    }

    clearAll() {
        this.selectedIntersectionIds.clear();
        this.selectedEdgeIds.clear();
        this.updateSelectedCount();
        this.updateSelectedList();
        this.drawNetwork();
        this.updateConfirmButton();
    }

    updateConfirmButton() {
        const hasSelection = (this.config.allow_intersection_selection && this.selectedIntersectionIds.size > 0) ||
                           (this.config.allow_edge_selection && this.selectedEdgeIds.size > 0);
        this.confirmBtn.disabled = !hasSelection;
    }

    updateSelectedCount() {
        let text = '已选择: ';
        let parts = [];
        
        if (this.config.allow_intersection_selection && this.selectedIntersectionIds.size > 0) {
            parts.push(`${this.selectedIntersectionIds.size}个交叉口`);
        }
        
        if (this.config.allow_edge_selection && this.selectedEdgeIds.size > 0) {
            parts.push(`${this.selectedEdgeIds.size}条路段`);
        }
        
        if (parts.length === 0) {
            text += '0';
        } else {
            text += parts.join(' + ');
        }
        
        this.selectedCountSpan.textContent = text;
    }

    updateSelectedList() {
        const hasIntersections = this.config.allow_intersection_selection && this.selectedIntersectionIds.size > 0;
        const hasEdges = this.config.allow_edge_selection && this.selectedEdgeIds.size > 0;
        
        if (!hasIntersections && !hasEdges) {
            this.selectedList.innerHTML = '<p class="empty-message">请在地图上点击选择元素</p>';
        } else {
            let items = '';
            
            // 添加交叉口
            if (hasIntersections) {
                items += '<h4 style="margin: 10px 0 5px 0; color: #3498db;">🚦 选中的交叉口:</h4>';
                items += Array.from(this.selectedIntersectionIds).sort().map(id => `
                    <div class="selected-item intersection-item">
                        <span>🚦 ${id}</span>
                        <button class="remove-btn" onclick="selector.toggleIntersectionSelection('${id}')">移除</button>
                    </div>
                `).join('');
            }
            
            // 添加路段
            if (hasEdges) {
                items += '<h4 style="margin: 10px 0 5px 0; color: #e74c3c;">🛣️ 选中的路段:</h4>';
                items += Array.from(this.selectedEdgeIds).sort().map(id => {
                    // 查找对应的路段对象以获取名称
                    const edge = this.edges.find(e => e.id === id);
                    const displayName = edge && edge.name && edge.name.trim() ? edge.name : id;
                    const tooltipText = edge && edge.name && edge.name.trim() ? `${edge.name} (${id})` : id;
                    
                    return `
                        <div class="selected-item edge-item" title="${tooltipText}">
                            <span>🛣️ ${displayName}</span>
                            <button class="remove-btn" onclick="selector.toggleEdgeSelection('${id}')">移除</button>
                        </div>
                    `;
                }).join('');
            }
            
            this.selectedList.innerHTML = items;
        }
    }

    async confirmSelection() {
        const hasSelection = (this.config.allow_intersection_selection && this.selectedIntersectionIds.size > 0) ||
                           (this.config.allow_edge_selection && this.selectedEdgeIds.size > 0);
        
        if (!hasSelection) {
            this.showStatus('请至少选择一个元素', 'error');
            return;
        }

        this.confirmBtn.disabled = true;
        this.confirmBtn.textContent = '正在确认...';

        try {
            const response = await fetch('/api/confirm_selection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    selected_intersection_ids: Array.from(this.selectedIntersectionIds).sort(),
                    selected_edge_ids: Array.from(this.selectedEdgeIds).sort()
                })
            });

            const result = await response.json();
            this.showStatus(result.message, 'success');
            
            setTimeout(() => {
                window.close();
            }, 2000);
            
        } catch (error) {
            this.showStatus('确认选择失败: ' + error.message, 'error');
            this.confirmBtn.disabled = false;
            this.confirmBtn.textContent = '确认选择';
        }
    }

    showStatus(message, type) {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
        this.status.style.display = 'block';
        
        setTimeout(() => {
            this.status.style.display = 'none';
        }, 3000);
    }

    getEdgeAt(mouseX, mouseY) {
        const tolerance = 15;
        let closestEdge = null;
        let closestDistance = Infinity;
        
        for (const edge of this.edges) {
            if (edge.points.length < 2) continue;
            
            for (let i = 0; i < edge.points.length - 1; i++) {
                const p1 = this.worldToScreen(edge.points[i].x, edge.points[i].y);
                const p2 = this.worldToScreen(edge.points[i + 1].x, edge.points[i + 1].y);
                
                const distance = this.distanceToLineSegment(mouseX, mouseY, p1.x, p1.y, p2.x, p2.y);
                
                if (distance <= tolerance && distance < closestDistance) {
                    closestDistance = distance;
                    closestEdge = edge;
                }
            }
        }
        
        return closestEdge;
    }

    distanceToLineSegment(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) {
            return Math.sqrt(A * A + B * B);
        }
        
        let param = dot / lenSq;
        let xx, yy;

        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = px - xx;
        const dy = py - yy;
        return Math.sqrt(dx * dx + dy * dy);
    }
}

// 全局变量供HTML调用
let selector; 