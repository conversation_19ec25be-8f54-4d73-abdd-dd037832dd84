<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仿真结果 - 大型活动管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #2b3a42; /* 设置背景颜色为深蓝色 */
        }
        .results-container {
            max-width: 1000px;
            margin: 0 auto;
            color: #ffffff; /* 设置文字颜色为白色 */
        }
        .result-card {
            margin-bottom: 10px;
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            background-color: #3e4e56; /* 设置卡片背景颜色为深灰色 */
        }
        .result-header {
            margin-bottom: 8px;
            padding-bottom: 6px;
            border-bottom: 1px solid #eee;
        }
        .result-header h2 {
            margin: 0;
            font-size: 1.2em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 8px;
            background-color: #4a5a65; /* 设置选项组背景颜色为稍浅的灰色 */
            border-radius: 8px;
            padding: 10px;
        }
        .metric-item {
            padding: 6px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #3366cc;
            margin-bottom: 2px;
        }
        .metric-label {
            font-size: 0.8em;
            color: #666;
        }
        .section-title {
            margin-top: 10px;
            margin-bottom: 6px;
            color: #333;
            font-size: 1.1em;
        }
        .config-item {
            margin-bottom: 4px;
            font-size: 0.9em;
        }
        .simulation-info {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            font-size: 0.9em;
        }
        .back-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #5a6a75; /* 设置按钮背景颜色 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .back-btn:hover {
            background-color: #4a5a65; /* 设置按钮悬停时的背景颜色 */
        }
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        @media (max-width: 480px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
        .chart-container {
            margin-top: 10px;
            height: 200px;
        }
        header h1 {
            font-size: 1.6em;
            margin: 10px 0;
        }
        .config-summary p {
            margin: 4px 0;
        }
        .config-summary {
            background-color: #4a5a65; /* 设置选项组背景颜色为稍浅的灰色 */
            border-radius: 8px;
            padding: 10px;
        }
        .comparison-controls p {
            color: #110f0f; /* 设置文字颜色为白色 */
        }
        #compareSimId {
            background-color: #4a6578; /* 设置输入框背景颜色 */
            color: #ffffff; /* 设置输入框文字颜色 */
            border: 1px solid #ffffff; /* 设置输入框边框颜色 */
        }
        .sim-tag {
            background-color: #5a6a75; /* 设置sim-tag背景颜色 */
            color: #110f0f; /* 设置sim-tag文字颜色 */
            border: 1px solid #ffffff; /* 设置sim-tag边框颜色 */
        }
        .remove-btn {
            color: #ff6666; /* 设置删除按钮颜色 */
        }
    </style>
</head>
<body>
    <div class="container results-container">
        <header>
            <h1>仿真结果分析</h1>
        </header>
        
        <main>
            <!-- 结果对比区域，放在最上面 -->
            <section class="result-card" id="comparisonSection" style="margin-top:20px;">
                <div class="result-header">
                    <h2>结果对比</h2>
                </div>
                <div class="comparison-controls">
                    <p>选择要对比的仿真结果：</p>
                    <div id="comparisonList" style="margin-bottom:10px;">
                        <!-- 这里将动态加载可比较的仿真ID列表 -->
                        <button id="loadComparisonBtn" class="back-btn" style="background-color:#3366cc;">加载对比数据</button>
                    </div>
                    <div style="display:flex; margin-bottom:10px;">
                        <input type="text" id="compareSimId" placeholder="输入仿真ID" style="padding:8px; margin-right:10px; flex:1;">
                        <button id="addCompareBtn" class="back-btn" style="background-color:#3366cc;">添加对比</button>
                    </div>
                </div>
                <div class="comparison-charts" style="display:none;">
                    <div class="chart-container" style="height:300px; margin-top:20px;">
                        <canvas id="comparisonChart"></canvas>
                    </div>
                    <div style="margin-top:15px;">
                        <select id="metricSelector" style="padding:8px; width:100%;">
                            <option value="travel_time">平均行程时间</option>
                            <option value="waiting_time">平均等待时间</option>
                            <option value="waiting_count">平均等待次数</option>
                            <option value="time_loss">平均时间损失</option>
                        </select>
                    </div>
                </div>
            </section>

            <!-- 仿真概况 -->
            <section class="result-card" id="simulationInfo">
                <div class="result-header">
                    <h2>仿真概况</h2>
                </div>
                <div class="simulation-info">
                    <div>
                        <span><strong>仿真ID:</strong> <span id="simId">250702174456</span></span>
                        <span style="margin-left:15px"><strong>开始时间:</strong> <span id="startTime">2025-07-02 17:44:56</span></span>
                    </div>
                </div>
            </section>

            <!-- 配置摘要 -->
            <section class="result-card" id="configSummary">
                <div class="result-header">
                    <h2>配置摘要</h2>
                </div>
                <div class="config-summary">
                    <div class="config-item" id="networkConfig">
                        <span><strong>路网配置:</strong> <span>预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)</span></span>
                    </div>
                    <div class="config-item" id="signalConfig">
                        <span><strong>信号配置:</strong> <span>预设配时 + 自定义优化(2个交叉口)</span></span>
                    </div>
                    <div class="config-item" id="trafficConfig">
                        <span><strong>交通需求:</strong> <span>预设需求 - 进场场景 + 贵宾专车</span></span>
                    </div>
                </div>
            </section>

            <div style="display:flex; flex-wrap:wrap; gap:10px; justify-content:space-between;">
                <section class="result-card" id="pedestrianMetrics" style="flex:1; min-width:48%;">
                    <div class="result-header">
                        <h2>行人指标</h2>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value" id="pedTravelTime">123.45</div>
                            <div class="metric-label">平均行程时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="pedWaitingTime">12.34</div>
                            <div class="metric-label">平均等待时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="pedWaitingCount">2.1</div>
                            <div class="metric-label">平均等待次数</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="pedTimeLoss">23.45</div>
                            <div class="metric-label">平均时间损失 (秒)</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="pedChart"></canvas>
                    </div>
                </section>

                <section class="result-card" id="vehicleMetrics" style="flex:1; min-width:48%;">
                    <div class="result-header">
                        <h2>一般车辆指标</h2>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value" id="vehTravelTime">234.56</div>
                            <div class="metric-label">平均行程时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="vehWaitingTime">45.67</div>
                            <div class="metric-label">平均等待时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="vehWaitingCount">3.2</div>
                            <div class="metric-label">平均等待次数</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="vehTimeLoss">67.89</div>
                            <div class="metric-label">平均时间损失 (秒)</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="vehChart"></canvas>
                    </div>
                </section>
            </div>

            <div style="display:flex; flex-wrap:wrap; gap:10px; justify-content:space-between; margin-top:10px;">
                <section class="result-card" id="vipVehicleMetrics" style="flex:1; min-width:48%;">
                    <div class="result-header">
                        <h2>贵宾专车指标</h2>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value" id="vipTravelTime">145.23</div>
                            <div class="metric-label">平均行程时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="vipWaitingTime">8.91</div>
                            <div class="metric-label">平均等待时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="vipWaitingCount">1.2</div>
                            <div class="metric-label">平均等待次数</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="vipTimeLoss">12.34</div>
                            <div class="metric-label">平均时间损失 (秒)</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="vipChart"></canvas>
                    </div>
                </section>

                <section class="result-card" id="venueAreaMetrics" style="flex:1; min-width:48%;">
                    <div class="result-header">
                        <h2>场馆区域指标</h2>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value" id="venuePedTime">156.78</div>
                            <div class="metric-label">行人平均行程时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="venuePedDelay">34.56</div>
                            <div class="metric-label">行人平均延误 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="venueVehTime">267.89</div>
                            <div class="metric-label">车辆平均行程时间 (秒)</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="venueVehDelay">78.90</div>
                            <div class="metric-label">车辆平均延误 (秒)</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="venueChart"></canvas>
                    </div>
                </section>
            </div>

            <button class="back-btn" onclick="window.location.href='index.html'">返回主页</button>
            
        </main>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 示例JSON数据（实际应用中可通过API获取或从文件读取）
            const resultData = {
                "simulation_id": "250702174456",
                "config_summary": {
                    "network": "预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)",
                    "signal": "预设配时 + 自定义优化(2个交叉口)",
                    "traffic": "预设需求 - 进场场景 + 贵宾专车"
                },
                "start_time": "2025-07-02T17:44:56.123456",
                "simulation_results": {
                    "pedestrian_metrics": {
                        "average_travel_time": 123.45,
                        "average_waiting_time": 12.34,
                        "average_waiting_count": 2.1,
                        "average_time_loss": 23.45
                    },
                    "vehicle_metrics": {
                        "average_travel_time": 234.56,
                        "average_waiting_time": 45.67,
                        "average_waiting_count": 3.2,
                        "average_time_loss": 67.89
                    },
                    "vip_vehicle_metrics": {
                        "average_travel_time": 145.23,
                        "average_waiting_time": 8.91,
                        "average_waiting_count": 1.2,
                        "average_time_loss": 12.34
                    },
                    "venue_area_metrics": {
                        "average_pedestrian_travel_time": 156.78,
                        "average_pedestrian_delay": 34.56,
                        "average_vehicle_travel_time": 267.89,
                        "average_vehicle_delay": 78.90
                    }
                }
            };

            // 函数：用于从URL参数或JSON文件加载数据
            async function loadResultData() {
                try {
                    // 尝试从URL参数获取JSON
                    const urlParams = new URLSearchParams(window.location.search);
                    const jsonData = urlParams.get('data');
                    
                    if (jsonData) {
                        return JSON.parse(decodeURIComponent(jsonData));
                    }
                    
                    // 尝试从文件加载JSON（实际应用可能需要更复杂的逻辑）
                    const urlParams2 = new URLSearchParams(window.location.search);
                    const fileParam = urlParams2.get('file');
                    
                    if (fileParam) {
                        const response = await fetch(fileParam);
                        if (response.ok) {
                            return await response.json();
                        }
                    }
                    
                    // 如果没有获取到数据，返回默认数据
                    return resultData;
                } catch (error) {
                    console.error("加载数据失败:", error);
                    return resultData; // 出错时使用默认数据
                }
            }

            // 函数：更新页面显示
            function updateDisplay(data) {
                // 更新仿真信息
                document.getElementById('simId').textContent = data.simulation_id;
                
                // 格式化并显示时间
                const startTimeRaw = new Date(data.start_time);
                const formattedTime = startTimeRaw.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }).replace(/\//g, '-');
                document.getElementById('startTime').textContent = formattedTime;
                
                // 更新配置摘要
                document.querySelector('#networkConfig span').textContent = data.config_summary.network;
                document.querySelector('#signalConfig span').textContent = data.config_summary.signal;
                document.querySelector('#trafficConfig span').textContent = data.config_summary.traffic;
                
                // 更新行人指标
                document.getElementById('pedTravelTime').textContent = data.simulation_results.pedestrian_metrics.average_travel_time.toFixed(2);
                document.getElementById('pedWaitingTime').textContent = data.simulation_results.pedestrian_metrics.average_waiting_time.toFixed(2);
                document.getElementById('pedWaitingCount').textContent = data.simulation_results.pedestrian_metrics.average_waiting_count.toFixed(1);
                document.getElementById('pedTimeLoss').textContent = data.simulation_results.pedestrian_metrics.average_time_loss.toFixed(2);
                
                // 更新一般车辆指标
                document.getElementById('vehTravelTime').textContent = data.simulation_results.vehicle_metrics.average_travel_time.toFixed(2);
                document.getElementById('vehWaitingTime').textContent = data.simulation_results.vehicle_metrics.average_waiting_time.toFixed(2);
                document.getElementById('vehWaitingCount').textContent = data.simulation_results.vehicle_metrics.average_waiting_count.toFixed(1);
                document.getElementById('vehTimeLoss').textContent = data.simulation_results.vehicle_metrics.average_time_loss.toFixed(2);
                
                // 更新贵宾专车指标
                document.getElementById('vipTravelTime').textContent = data.simulation_results.vip_vehicle_metrics.average_travel_time.toFixed(2);
                document.getElementById('vipWaitingTime').textContent = data.simulation_results.vip_vehicle_metrics.average_waiting_time.toFixed(2);
                document.getElementById('vipWaitingCount').textContent = data.simulation_results.vip_vehicle_metrics.average_waiting_count.toFixed(1);
                document.getElementById('vipTimeLoss').textContent = data.simulation_results.vip_vehicle_metrics.average_time_loss.toFixed(2);
                
                // 更新场馆区域指标
                document.getElementById('venuePedTime').textContent = data.simulation_results.venue_area_metrics.average_pedestrian_travel_time.toFixed(2);
                document.getElementById('venuePedDelay').textContent = data.simulation_results.venue_area_metrics.average_pedestrian_delay.toFixed(2);
                document.getElementById('venueVehTime').textContent = data.simulation_results.venue_area_metrics.average_vehicle_travel_time.toFixed(2);
                document.getElementById('venueVehDelay').textContent = data.simulation_results.venue_area_metrics.average_vehicle_delay.toFixed(2);

                // 创建柱状图
                createPedestrianChart(data.simulation_results.pedestrian_metrics);
                createVehicleChart(data.simulation_results.vehicle_metrics);
                createVIPVehicleChart(data.simulation_results.vip_vehicle_metrics);
                createVenueChart(data.simulation_results.venue_area_metrics);
            }
            
            // 创建行人指标柱状图
            function createPedestrianChart(metrics) {
                const ctx = document.getElementById('pedChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [{
                            label: '行人指标',
                            data: [
                                metrics.average_travel_time,
                                metrics.average_waiting_time,
                                metrics.average_waiting_count,
                                metrics.average_time_loss
                            ],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 206, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色
                                }
                            },
                            title: {
                                color: '#ffffff' // 设置标题文字颜色为白色
                            }
                        }
                    }
                });
            }

            // 创建一般车辆指标柱状图
            function createVehicleChart(metrics) {
                const ctx = document.getElementById('vehChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [{
                            label: '一般车辆指标',
                            data: [
                                metrics.average_travel_time,
                                metrics.average_waiting_time,
                                metrics.average_waiting_count,
                                metrics.average_time_loss
                            ],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.6)',
                                'rgba(255, 159, 64, 0.6)',
                                'rgba(255, 205, 86, 0.6)',
                                'rgba(75, 192, 192, 0.6)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(255, 159, 64, 1)',
                                'rgba(255, 205, 86, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色为白色
                                }
                            }
                        }
                    }
                });
            }

            // 创建贵宾专车指标柱状图
            function createVIPVehicleChart(metrics) {
                const ctx = document.getElementById('vipChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [{
                            label: '贵宾专车指标',
                            data: [
                                metrics.average_travel_time,
                                metrics.average_waiting_time,
                                metrics.average_waiting_count,
                                metrics.average_time_loss
                            ],
                            backgroundColor: [
                                'rgba(153, 102, 255, 0.6)',
                                'rgba(54, 162, 235, 0.6)',
                                'rgba(75, 192, 192, 0.6)',
                                'rgba(255, 99, 132, 0.6)'
                            ],
                            borderColor: [
                                'rgba(153, 102, 255, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色为白色
                                }
                            }
                        }
                    }
                });
            }

            // 创建场馆区域指标柱状图
            function createVenueChart(metrics) {
                const ctx = document.getElementById('venueChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['行人平均行程时间', '行人平均延误', '车辆平均行程时间', '车辆平均延误'],
                        datasets: [{
                            label: '场馆区域指标',
                            data: [
                                metrics.average_pedestrian_travel_time,
                                metrics.average_pedestrian_delay,
                                metrics.average_vehicle_travel_time,
                                metrics.average_vehicle_delay
                            ],
                            backgroundColor: [
                                'rgba(255, 205, 86, 0.6)',
                                'rgba(75, 192, 192, 0.6)',
                                'rgba(54, 162, 235, 0.6)',
                                'rgba(153, 102, 255, 0.6)'
                            ],
                            borderColor: [
                                'rgba(255, 205, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(153, 102, 255, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色为白色
                                }
                            }
                        }
                    }
                });
            }
            
            // 多结果对比相关功能
            let comparisonData = {};
            let comparisonChart = null;
            
            // 添加仿真结果到对比列表
            function addSimulationToComparison(simId) {
                if (!simId || simId.trim() === '') return;
                
                // 检查是否已存在
                if (comparisonData[simId]) {
                    alert('该仿真ID已添加到对比列表');
                    return;
                }
                
                // 从本地 sumo_data/仿真id/ 文件夹读取JSON文件
                const fetchSimulationData = async (id) => {
                    try {
                        // 构建JSON文件的URL路径
                        const jsonFilePath = `../backend/sumo_data/${id}/simulation_result_${id}.json`;
                        
                        console.log(`正在尝试加载仿真数据: ${jsonFilePath}`);
                        
                        const response = await fetch(jsonFilePath);
                        
                        if (!response.ok) {
                            throw new Error(`无法找到仿真ID "${id}" 的结果文件`);
                        }
                        
                        const jsonData = await response.json();
                        
                        // 验证JSON数据格式
                        if (!validateSimulationJSON(jsonData)) {
                            throw new Error(`仿真ID "${id}" 的数据格式不符合要求`);
                        }
                        
                        console.log(`成功加载仿真数据: ${id}`);
                        return jsonData;
                        
                    } catch (error) {
                        console.error(`加载仿真数据失败: ${error.message}`);
                        throw error;
                    }
                };
                
                // 显示加载状态
                const originalButton = document.getElementById('addCompareBtn');
                const originalText = originalButton ? originalButton.textContent : '';
                if (originalButton) {
                    originalButton.textContent = '加载中...';
                    originalButton.disabled = true;
                }
                
                fetchSimulationData(simId).then(data => {
                    // 保存数据
                    comparisonData[simId] = data;
                    
                    // 更新UI
                    updateComparisonList();
                    
                    // 如果是第一个添加的数据，显示图表区域
                    if (Object.keys(comparisonData).length === 1) {
                        document.querySelector('.comparison-charts').style.display = 'block';
                    }
                    
                    // 更新对比图表
                    updateComparisonChart();
                    
                    // 显示成功提示
                    alert(`成功添加仿真数据: ${simId}`);
                    
                }).catch(error => {
                    console.error("获取仿真数据失败:", error);
                    alert(`获取仿真数据失败: ${error.message}`);
                }).finally(() => {
                    // 恢复按钮状态
                    if (originalButton) {
                        originalButton.textContent = originalText;
                        originalButton.disabled = false;
                    }
                });
            }
            
            // 更新对比列表UI
            function updateComparisonList() {
                const container = document.getElementById('comparisonList');
                // 清除现有内容，保留按钮
                const loadBtn = document.getElementById('loadComparisonBtn');
                container.innerHTML = '';
                container.appendChild(loadBtn);
                
                // 添加每个仿真的标签
                Object.keys(comparisonData).forEach(simId => {
                    const simTag = document.createElement('div');
                    simTag.className = 'sim-tag';
                    simTag.style.display = 'inline-block';
                    simTag.style.padding = '5px 10px';
                    simTag.style.margin = '0 5px 5px 0';
                    simTag.style.backgroundColor = '#f0f0f0';
                    simTag.style.borderRadius = '4px';
                    simTag.style.border = '1px solid #ccc';
                    
                    simTag.innerHTML = `
                        <span>${simId}</span>
                        <button class="remove-btn" data-simid="${simId}" style="border:none; background:none; color:red; cursor:pointer; margin-left:5px;">✕</button>
                    `;
                    
                    container.insertBefore(simTag, loadBtn);
                });
                
                // 添加删除事件
                document.querySelectorAll('.remove-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const simId = this.getAttribute('data-simid');
                        delete comparisonData[simId];
                        updateComparisonList();
                        updateComparisonChart();
                        
                        // 如果没有数据了，隐藏图表区域
                        if (Object.keys(comparisonData).length === 0) {
                            document.querySelector('.comparison-charts').style.display = 'none';
                        }
                    });
                });
            }
            
            // 验证仿真结果JSON格式
            function validateSimulationJSON(jsonData) {
                // 检查必需的顶级字段
                if (!jsonData.simulation_id || !jsonData.simulation_results) {
                    console.error('缺少必需字段: simulation_id 或 simulation_results');
                    return false;
                }
                
                // 检查simulation_results的结构
                const results = jsonData.simulation_results;
                const requiredMetrics = ['pedestrian_metrics', 'vehicle_metrics', 'vip_vehicle_metrics'];
                
                for (const metric of requiredMetrics) {
                    if (!results[metric]) {
                        console.error(`缺少必需的指标: ${metric}`);
                        return false;
                    }
                    
                    // 检查每个指标是否包含必需的字段
                    const requiredFields = ['average_travel_time', 'average_waiting_time', 'average_waiting_count', 'average_time_loss'];
                    for (const field of requiredFields) {
                        if (typeof results[metric][field] !== 'number') {
                            console.error(`指标 ${metric} 中的字段 ${field} 不是数字类型`);
                            return false;
                        }
                    }
                }
                
                // 检查是否包含venue_area_metrics（可选）
                if (results.venue_area_metrics) {
                    const venueFields = ['average_pedestrian_travel_time', 'average_pedestrian_delay', 'average_vehicle_travel_time', 'average_vehicle_delay'];
                    for (const field of venueFields) {
                        if (results.venue_area_metrics[field] !== undefined && typeof results.venue_area_metrics[field] !== 'number') {
                            console.error(`venue_area_metrics 中的字段 ${field} 不是数字类型`);
                            return false;
                        }
                    }
                }
                
                console.log('JSON数据格式验证通过');
                return true;
            }
            
            // 更新对比图表
            function updateComparisonChart() {
                const metricType = document.getElementById('metricSelector').value;
                const metricLabels = {
                    'travel_time': '平均行程时间',
                    'waiting_time': '平均等待时间',
                    'waiting_count': '平均等待次数',
                    'time_loss': '平均时间损失'
                };
                
                const metricKeys = {
                    'travel_time': 'average_travel_time',
                    'waiting_time': 'average_waiting_time',
                    'waiting_count': 'average_waiting_count',
                    'time_loss': 'average_time_loss'
                };
                
                const datasets = [];
                const categories = ['行人', '一般车辆', '贵宾专车'];
                const simIds = Object.keys(comparisonData);
                
                if (simIds.length === 0) return;
                
                // 为每个仿真创建数据集
                simIds.forEach((simId, index) => {
                    const data = [
                        comparisonData[simId].simulation_results.pedestrian_metrics[metricKeys[metricType]],
                        comparisonData[simId].simulation_results.vehicle_metrics[metricKeys[metricType]],
                        comparisonData[simId].simulation_results.vip_vehicle_metrics[metricKeys[metricType]]
                    ];
                    
                    // 随机生成颜色，但确保颜色不会太相似
                    const hue = (index * 137) % 360; // 使用黄金比例分布色相
                    const bgColor = `hsla(${hue}, 70%, 60%, 0.6)`;
                    const borderColor = `hsla(${hue}, 70%, 60%, 1)`;
                    
                    datasets.push({
                        label: `仿真 ${simId}`,
                        data: data,
                        backgroundColor: bgColor,
                        borderColor: borderColor,
                        borderWidth: 1
                    });
                });
                
                // 创建或更新图表
                const ctx = document.getElementById('comparisonChart').getContext('2d');
                
                if (comparisonChart) {
                    comparisonChart.destroy();
                }
                
                comparisonChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: categories,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `各仿真结果 ${metricLabels[metricType]} 对比`,
                                color: '#ffffff' // 设置标题文字颜色为白色
                            },
                            legend: {
                                position: 'top',
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色为白色
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: metricType.includes('count') ? '次数' : '时间 (秒)',
                                    color: '#ffffff' // 设置y轴标题文字颜色为白色
                                },
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        }
                    }
                });
            }
            
            // 加载数据并更新显示
            loadResultData().then(data => {
                updateDisplay(data);
                
                // 添加当前仿真结果到对比列表
                addSimulationToComparison(data.simulation_id);
                
                // 添加事件监听
                document.getElementById('addCompareBtn').addEventListener('click', function() {
                    const simId = document.getElementById('compareSimId').value;
                    addSimulationToComparison(simId);
                    document.getElementById('compareSimId').value = '';
                });
                
                document.getElementById('loadComparisonBtn').addEventListener('click', function() {
                    // 创建文件输入元素
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = '.json';
                    // 不设置multiple属性，默认只能选择一个文件
                    
                    fileInput.onchange = function(e) {
                        const file = e.target.files[0];
                        if (!file) return;
                        
                        // 验证文件类型
                        if (!file.name.toLowerCase().endsWith('.json')) {
                            alert(`文件 "${file.name}" 不是JSON格式`);
                            return;
                        }
                        
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            try {
                                const jsonData = JSON.parse(e.target.result);
                                
                                // 验证JSON数据格式
                                if (validateSimulationJSON(jsonData)) {
                                    // 使用文件名（去掉扩展名）作为仿真ID，或使用JSON中的simulation_id
                                    const simId = jsonData.simulation_id || file.name.replace('.json', '');
                                    
                                    // 检查是否已存在相同ID的数据
                                    if (comparisonData[simId]) {
                                        if (!confirm(`仿真数据 "${simId}" 已存在，是否覆盖？`)) {
                                            return;
                                        }
                                    }
                                    
                                    // 添加到对比数据中
                                    comparisonData[simId] = jsonData;
                                    
                                    // 更新UI
                                    updateComparisonList();
                                    
                                    // 如果是第一个添加的数据，显示图表区域
                                    if (Object.keys(comparisonData).length === 1) {
                                        document.querySelector('.comparison-charts').style.display = 'block';
                                    }
                                    
                                    // 更新对比图表
                                    updateComparisonChart();
                                    
                                    console.log(`成功加载对比数据: ${simId}`);
                                    alert(`成功加载对比数据: ${simId}`);
                                } else {
                                    alert(`文件 "${file.name}" 的JSON格式不符合仿真结果格式要求`);
                                }
                            } catch (error) {
                                console.error('JSON解析失败:', error);
                                alert(`文件 "${file.name}" 不是有效的JSON格式`);
                            }
                        };
                        
                        reader.onerror = function() {
                            alert(`读取文件 "${file.name}" 失败`);
                        };
                        
                        reader.readAsText(file);
                    };
                    
                    // 触发文件选择对话框
                    fileInput.click();
                });
                
                document.getElementById('metricSelector').addEventListener('change', updateComparisonChart);
            });
        });
    </script>
</body>
</html> 