import xml.etree.ElementTree as ET
from collections import defaultdict
import os

class MetricsCalculator:
    def __init__(self, sim_id):
        """
        初始化指标计算器
        
        参数:
        sim_id: str - 仿真ID，用于定位输出文件
        """
        self.base_dir = 'sumo_data'
        self.sim_dir = os.path.join(self.base_dir, sim_id)
        
    def calculate_metrics(self, config):
        """
        根据仿真类型计算相应指标
        
        参数:
        config: dict - JSON配置
        """
        metrics = {}
        
        # 基础指标：行人和车辆
        metrics.update(self._calculate_pedestrian_metrics())
        metrics.update(self._calculate_vehicle_metrics())
        
        # 根据配置决定计算哪些额外指标
        traffic_config = config['traffic_config']
        
        # 检查是否为VIP场景
        if (traffic_config.get('type') == 'predefined' and 
            traffic_config.get('vehicle_type') == '存在贵宾专车'):
            metrics.update(self._calculate_vip_metrics())
        
        # 检查是否为离场场景
        if (traffic_config.get('type') == 'predefined' and 
            traffic_config.get('scenario') == '离场'):
            metrics.update(self._calculate_venue_metrics())
            
        return metrics
    
    def _calculate_pedestrian_metrics(self):
        """计算行人相关指标"""
        metrics = {}
        
        # 读取tripinfo文件
        tripinfo_file = os.path.join(self.sim_dir, 'output.tripinfo.xml')
        if os.path.exists(tripinfo_file):
            tree = ET.parse(tripinfo_file)
            root = tree.getroot()
            
            # 统计数据
            total_duration = 0
            total_waitingTime = 0
            total_waitingCount = 0
            total_timeLoss = 0
            count = 0
            
            # 从personinfo中获取行人数据
            for personinfo in root.findall('personinfo'):
                if personinfo.get('id').startswith('gym_people'):
                    total_duration += float(personinfo.get('duration', 0))
                    total_waitingTime += float(personinfo.get('waitingTime', 0))
                    total_waitingCount += int(personinfo.get('waitingCount', 0))
                    total_timeLoss += float(personinfo.get('timeLoss', 0))
                    count += 1
            
            # 从tripinfo中获取行人数据
            for tripinfo in root.findall('tripinfo'):
                if tripinfo.get('id').startswith('gym_people'):
                    total_duration += float(tripinfo.get('duration', 0))
                    total_waitingTime += float(tripinfo.get('waitingTime', 0))
                    total_waitingCount += int(tripinfo.get('waitingCount', 0))
                    total_timeLoss += float(tripinfo.get('timeLoss', 0))
                    count += 1
            
            if count > 0:
                metrics['avg_duration'] = total_duration / count
                metrics['avg_waitingTime'] = total_waitingTime / count
                metrics['avg_waitingCount'] = total_waitingCount / count
                metrics['avg_timeLoss'] = total_timeLoss / count
            else:
                print("No pedestrian entries were found.")
                metrics = {
                    'avg_duration': 0,
                    'avg_waitingTime': 0,
                    'avg_waitingCount': 0,
                    'avg_timeLoss': 0
                }
        
        return {'pedestrian_metrics': metrics}
    
    def _calculate_vehicle_metrics(self):
        """计算车辆相关指标"""
        metrics = {}
        
        tripinfo_file = os.path.join(self.sim_dir, 'output.tripinfo.xml')
        if os.path.exists(tripinfo_file):
            tree = ET.parse(tripinfo_file)
            root = tree.getroot()
            
            # 统计一般车辆的信息
            vehicle_data = defaultdict(list)
            for tripinfo in root.findall('tripinfo'):
                # 使用统一的前缀，排除VIP车辆
                if tripinfo.get('id').startswith('gym_vehicle') and not tripinfo.get('id').startswith('gym_vip'):
                    vehicle_data['duration'].append(float(tripinfo.get('duration')))
                    vehicle_data['waiting_time'].append(float(tripinfo.get('waitingTime')))
                    vehicle_data['waiting_count'].append(float(tripinfo.get('waitingCount', 0)))
                    vehicle_data['time_loss'].append(float(tripinfo.get('timeLoss')))
            
            # 计算平均值
            if vehicle_data['duration']:  # 如果有数据
                metrics['avg_duration'] = sum(vehicle_data['duration']) / len(vehicle_data['duration'])
                metrics['avg_waiting_time'] = sum(vehicle_data['waiting_time']) / len(vehicle_data['waiting_time'])
                metrics['avg_waiting_count'] = sum(vehicle_data['waiting_count']) / len(vehicle_data['waiting_count'])
                metrics['avg_time_loss'] = sum(vehicle_data['time_loss']) / len(vehicle_data['time_loss'])
            else:
                metrics['avg_duration'] = 0
                metrics['avg_waiting_time'] = 0
                metrics['avg_waiting_count'] = 0
                metrics['avg_time_loss'] = 0
        
        return {'vehicle_metrics': metrics}
    
    def _calculate_vip_metrics(self):
        """计算贵宾专车相关指标"""
        metrics = {}
        
        tripinfo_file = os.path.join(self.sim_dir, 'output.tripinfo.xml')
        if os.path.exists(tripinfo_file):
            tree = ET.parse(tripinfo_file)
            root = tree.getroot()
            
            # 统计VIP车辆的信息
            vip_data = defaultdict(list)
            for tripinfo in root.findall('tripinfo'):
                if tripinfo.get('id').startswith('vip'):
                    vip_data['duration'].append(float(tripinfo.get('duration')))
                    vip_data['waiting_time'].append(float(tripinfo.get('waitingTime')))
                    vip_data['waiting_count'].append(float(tripinfo.get('waitingCount', 0)))
                    vip_data['time_loss'].append(float(tripinfo.get('timeLoss')))
            
            # 计算平均值
            if vip_data['duration']:  # 如果有数据
                metrics['vip_avg_duration'] = sum(vip_data['duration']) / len(vip_data['duration'])
                metrics['vip_avg_waiting_time'] = sum(vip_data['waiting_time']) / len(vip_data['waiting_time'])
                metrics['vip_avg_waiting_count'] = sum(vip_data['waiting_count']) / len(vip_data['waiting_count'])
                metrics['vip_avg_time_loss'] = sum(vip_data['time_loss']) / len(vip_data['time_loss'])
            else:
                metrics['vip_avg_duration'] = 0
                metrics['vip_avg_waiting_time'] = 0
                metrics['vip_avg_waiting_count'] = 0
                metrics['vip_avg_time_loss'] = 0
        
        return {'vip_metrics': metrics}
    
    def _calculate_venue_metrics(self):
        """计算场馆周围指标（离场场景）"""
        # 解析行人的edgedata XML文件
        people_edgedata_path = os.path.join(self.sim_dir, 'output.people_edgedata.xml')
        try:
            people_edge_tree = ET.parse(people_edgedata_path)
            people_edge_root = people_edge_tree.getroot()
        except ET.ParseError as e:
            print(f"Error parsing the people edgedata XML file: {e}")
            return None
        except FileNotFoundError:
            print("The people edgedata file was not found.")
            return None

        # 计算行人的统计数据
        people_total_traveltime = 0
        people_total_timeloss = 0
        people_total_departed = 0

        # 遍历所有edge计算行人数据
        for interval in people_edge_root.findall('interval'):
            for edge in interval.findall('edge'):
                departed = float(edge.get('departed', 0))
                if departed > 0:
                    traveltime = float(edge.get('traveltime', 0))
                    timeloss = float(edge.get('timeLoss', 0))
                    
                    people_total_traveltime += traveltime * departed
                    people_total_timeloss += timeloss 
                    people_total_departed += departed

        # 计算行人平均值
        people_avg_traveltime = people_total_traveltime / people_total_departed if people_total_departed > 0 else 0
        people_avg_timeloss = people_total_timeloss / people_total_departed if people_total_departed > 0 else 0

        # 解析车辆的edgedata XML文件
        vehicle_edgedata_path = os.path.join(self.sim_dir, 'output.vehicle_edgedata.xml')
        try:
            vehicle_edge_tree = ET.parse(vehicle_edgedata_path)
            vehicle_edge_root = vehicle_edge_tree.getroot()
        except ET.ParseError as e:
            print(f"Error parsing the vehicle edgedata XML file: {e}")
            return None
        except FileNotFoundError:
            print("The vehicle edgedata file was not found.")
            return None

        # 计算车辆的统计数据
        vehicle_total_traveltime = 0
        vehicle_total_timeloss = 0
        vehicle_total_departed = 0

        # 遍历所有edge计算车辆数据
        for interval in vehicle_edge_root.findall('interval'):
            for edge in interval.findall('edge'):
                departed = float(edge.get('departed', 0))
                if departed > 0:
                    traveltime = float(edge.get('traveltime', 0))
                    timeloss = float(edge.get('timeLoss', 0))
                    
                    vehicle_total_traveltime += traveltime * departed
                    vehicle_total_timeloss += timeloss 
                    vehicle_total_departed += departed

        # 计算车辆平均值
        vehicle_avg_traveltime = vehicle_total_traveltime / vehicle_total_departed if vehicle_total_departed > 0 else 0
        vehicle_avg_timeloss = vehicle_total_timeloss / vehicle_total_departed if vehicle_total_departed > 0 else 0
        
        return {'venue_metrics': {
            'people_avg_traveltime': people_avg_traveltime,
            'people_avg_timeloss': people_avg_timeloss,
            'vehicle_avg_traveltime': vehicle_avg_traveltime,
            'vehicle_avg_timeloss': vehicle_avg_timeloss
        }} 