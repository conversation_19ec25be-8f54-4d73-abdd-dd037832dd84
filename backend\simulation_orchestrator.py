import xml.etree.ElementTree as ET
import os
import subprocess
import traci
from datetime import datetime
from typing import Dict, Any

from config_parser import ConfigParser

class SimulationOrchestrator:
    """仿真编排器，负责根据新的JSON配置格式运行仿真"""
    
    def __init__(self):
        self.base_dir = './sumo_data'
        self.template_dir = os.path.join(self.base_dir, 'templates')
        self.tools_path = './tools'  # SUMO tools路径
        self.config_parser = ConfigParser()
    
    def run_simulation(self, config: Dict[str, Any]) -> str:
        """
        运行SUMO仿真
        
        参数:
        config: dict - 新格式的JSON配置
        
        返回:
        str - 仿真ID
        """
        # 1. 创建临时工作目录
        sim_id = self._create_temp_directory()
        
        print(f"开始仿真，ID: {sim_id}")
        
        try:
            # 2. 生成/准备路由文件
            route_file = self._prepare_route_file(sim_id, config)
            
            # 3. 生成SUMO命令
            sumo_cmd = self._generate_sumo_command(sim_id, config, route_file)
            
            # 4. 启动并运行仿真
            self._run_sumo_simulation(sim_id, config, sumo_cmd)
            
            print(f"仿真完成，ID: {sim_id}")
            return sim_id
            
        except Exception as e:
            print(f"仿真失败: {str(e)}")
            raise
    
    def _create_temp_directory(self) -> str:
        """创建临时工作目录"""
        # 使用年月日时分秒格式创建目录名
        sim_id = datetime.now().strftime("%y%m%d%H%M%S")
        
        # 创建目录
        temp_dir = os.path.join(self.base_dir, sim_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        # 创建weight子目录
        weight_dir = os.path.join(temp_dir, 'weight')
        os.makedirs(weight_dir, exist_ok=True)
        
        return sim_id
    
    def _prepare_route_file(self, sim_id: str, config: Dict[str, Any]) -> str:
        """准备路由文件"""
        # 检查是否使用自定义路由文件
        custom_route_file = self.config_parser.get_route_file(config)
        
        if custom_route_file:
            # 使用自定义路由文件
            print(f"使用自定义路由文件: {custom_route_file}")
            return custom_route_file
        else:
            # 生成预设交通流
            print("生成预设交通流...")
            return self._generate_predefined_traffic(sim_id, config)
    
    def _generate_predefined_traffic(self, sim_id: str, config: Dict[str, Any]) -> str:
        """生成预设交通流"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        
        # 1. 确定使用的路网文件（如果启用限行，先创建限行路网）
        net_file = self._get_network_file_for_traffic_generation(sim_id, config)
        
        # 2. 生成背景交通流
        self._generate_background_traffic(sim_id, config, net_file)
        
        # 3. 生成活动交通流
        self._generate_activity_traffic(sim_id, config, net_file)
        
        # 4. 合并交通流
        final_route_file = self._merge_traffic_flows(sim_id, config)
        
        # 5. 如果是进场场景，添加停车信息
        if self.config_parser.get_scenario_type(config) == '进场':
            self._add_parking_stops(final_route_file)
        
        return final_route_file
    
    def _get_network_file_for_traffic_generation(self, sim_id: str, config: Dict[str, Any]) -> str:
        """获取用于交通流生成的路网文件"""
        # 获取原始路网文件
        original_net_file = self.config_parser.get_network_file(config)
        
        # 如果启用道路限行，创建并返回限行路网文件
        if self.config_parser.is_road_restriction_enabled(config):
            print("创建限行路网用于交通流生成...")
            return self._create_restricted_network(sim_id, original_net_file, config)
        else:
            return original_net_file
    
    def _generate_background_traffic(self, sim_id: str, config: Dict[str, Any], net_file: str):
        """生成背景交通流"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        
        # 清理旧的背景交通流文件
        for file in os.listdir(temp_dir):
            if file.startswith('background'):
                os.remove(os.path.join(temp_dir, file))
        
        if not self.config_parser.is_road_restriction_enabled(config):
            # 无限行时，生成正常交通流
            # 生成小汽车交通流
            passenger_out = os.path.join(temp_dir, 'background_passenger.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', passenger_out,
                '--vehicle-class', 'passenger',
                '--prefix', 'background_passenger'
            ], check=True)
            
            # 生成行人交通流
            pedestrian_out = os.path.join(temp_dir, 'background_pedestrian.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', pedestrian_out,
                '--vehicle-class', 'pedestrian',
                '--prefix', 'background_pedestrian',
                '--pedestrians',
                '-p', '0.5',
                '--min-distance', '300'
            ], check=True)
        else:
            # 有限行时，使用限行路网生成背景交通流
            print("使用限行路网生成背景交通流...")
            
            # 生成行人交通流（行人不受限行影响）
            pedestrian_out = os.path.join(temp_dir, 'background_pedestrian.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', pedestrian_out,
                '--vehicle-class', 'pedestrian',
                '--prefix', 'pedestrian',
                '--pedestrians',
                '-p', '0.5',
                '--min-distance', '300'
            ], check=True)
            
            # 生成私家车交通流（会自动避开限行路段）
            private_out = os.path.join(temp_dir, 'background_private.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', private_out,
                '--vehicle-class', 'private',
                '--prefix', 'private'
            ], check=True)
        
        # 合并所有生成的路由文件到background.rou.xml
        self._merge_background_route_files(sim_id)
    
    def _merge_background_route_files(self, sim_id: str):
        """合并临时目录中的所有背景路由文件到background.rou.xml"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        output_file = os.path.join(temp_dir, 'background.rou.xml')
        
        # 获取所有.rou.xml文件
        input_files = [f for f in os.listdir(temp_dir) if f.endswith('.rou.xml') 
                      and f.startswith('background_')]
        
        root = ET.Element('routes')
        vehicles = []
        
        # 合并所有路由文件
        for file_name in input_files:
            file_path = os.path.join(temp_dir, file_name)
            tree = ET.parse(file_path)
            for elem in tree.getroot():
                if elem.tag in ['vehicle', 'person']:
                    vehicles.append(elem)
                else:
                    root.append(elem)
        
        # 按出发时间排序
        vehicles.sort(key=lambda v: float(v.get('depart')))
        
        # 添加排序后的车辆
        for vehicle in vehicles:
            root.append(vehicle)
            
        # 保存合并后的文件
        tree = ET.ElementTree(root)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
    
    def _generate_activity_traffic(self, sim_id: str, config: Dict[str, Any], net_file: str):
        """生成活动相关的交通流"""
        # 创建weight文件夹
        weight_dir = os.path.join(self.base_dir, sim_id, 'weight')
        
        # 如果weight文件夹已存在，先清空它
        if os.path.exists(weight_dir):
            # 删除所有现有的权重文件
            for file in os.listdir(weight_dir):
                if file.endswith('.src.xml') or file.endswith('.dst.xml'):
                    os.remove(os.path.join(weight_dir, file))
        else:
            # 创建weight文件夹
            os.makedirs(weight_dir)
        
        # 创建车辆和行人的权重文件
        self._create_vehicle_weights(weight_dir, config)
        self._create_pedestrian_weights(weight_dir, config)
        
        # 生成活动相关的车辆和行人流
        self._generate_activity_routes(sim_id, config, net_file)
    
    def _create_vehicle_weights(self, weight_dir: str, config: Dict[str, Any]):
        """创建车辆权重文件"""
        scenario_type = self.config_parser.get_scenario_type(config)
        
        # 根据进场/离场场景确定文件名
        if scenario_type == '进场':
            weight_file = os.path.join(weight_dir, 'weight_vehicle.dst.xml')
        else:  # 离场
            weight_file = os.path.join(weight_dir, 'weight_vehicle.src.xml')
        
        # 创建权重文件的XML结构
        root = ET.Element('edgedata')
        interval = ET.SubElement(root, 'interval')
        interval.set('begin', '0')
        interval.set('end', '3600')
        
        # 根据场景设置权重
        entrance_plan = self.config_parser.get_entrance_plan(config)
        if entrance_plan == '仅开放东侧出入口':
            # 添加east_1到east_12的权重
            for i in range(1, 13):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'east_{i}')
                edge.set('value', '2')
        elif entrance_plan == '仅开放南侧出入口':
            # 添加south_1到south_24的权重
            for i in range(1, 25):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'south_{i}')
                edge.set('value', '1')
        else:  # 全部开放
            # 添加east_1到east_12的权重
            for i in range(1, 13):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'east_{i}')
                edge.set('value', '2')
            
            # 添加south_1到south_24的权重
            for i in range(1, 25):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'south_{i}')
                edge.set('value', '1')
        
        # 保存格式化后的权重文件
        self._save_xml_file(root, weight_file)
    
    def _create_pedestrian_weights(self, weight_dir: str, config: Dict[str, Any]):
        """创建行人权重文件"""
        scenario_type = self.config_parser.get_scenario_type(config)
        
        # 根据进场/离场场景确定文件名
        if scenario_type == '进场':
            weight_file = os.path.join(weight_dir, 'weight_pedestrian.dst.xml')
        else:  # 离场
            weight_file = os.path.join(weight_dir, 'weight_pedestrian.src.xml')
        
        # 创建权重文件的XML结构
        root = ET.Element('edgedata')
        interval = ET.SubElement(root, 'interval')
        interval.set('begin', '0')
        interval.set('end', '3600')
        
        # 根据场景设置权重
        entrance_plan = self.config_parser.get_entrance_plan(config)
        if entrance_plan == '仅开放东侧出入口':
            edge = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge.set('id', 'people_east_in')
            else:
                edge.set('id', 'people_east_out')
            edge.set('value', '1')
        elif entrance_plan == '仅开放南侧出入口':
            edge = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge.set('id', 'people_south_in')
            else:
                edge.set('id', 'people_south_out')
            edge.set('value', '1')
        else:  # 全部开放
            # 添加东侧入口/出口
            edge1 = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge1.set('id', 'people_east_in')
            else:
                edge1.set('id', 'people_east_out')
            edge1.set('value', '1')
            
            # 添加南侧入口/出口
            edge2 = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge2.set('id', 'people_south_in')
            else:
                edge2.set('id', 'people_south_out')
            edge2.set('value', '1')
        
        # 保存格式化后的权重文件
        self._save_xml_file(root, weight_file)
    
    def _save_xml_file(self, root, file_path):
        """保存格式化的XML文件"""
        xml_str = ET.tostring(root, encoding='unicode')
        from xml.dom import minidom
        pretty_xml = minidom.parseString(xml_str).toprettyxml(indent='    ')
        
        # 移除空行
        pretty_xml = '\n'.join([line for line in pretty_xml.split('\n') if line.strip()])
        
        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)
    
    def _generate_activity_routes(self, sim_id: str, config: Dict[str, Any], net_file: str):
        """生成活动相关的路由文件"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        weight_dir = os.path.join(temp_dir, 'weight')
        
        # 生成车辆和行人路由文件
        route_configs = [
            ('gym_vehicle', False, 2, 'weight_vehicle'),
            ('gym_people', True, 0.3, 'weight_pedestrian')
        ]
        
        for prefix, is_person, p, weight_prefix in route_configs:
            out_file = os.path.join(temp_dir, f'{prefix}.rou.xml')
            cmd = [
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', out_file,
                '--vehicle-class', 'pedestrian' if is_person else 'passenger',
                '--prefix', prefix,
                '-p', str(p),
                '--weights-prefix', os.path.join(weight_dir, weight_prefix)
            ]
            if is_person:
                cmd.append('--pedestrians')
                cmd.append('--min-distance')
                cmd.append('300')
            subprocess.run(cmd, check=True)
    
    def _merge_traffic_flows(self, sim_id: str, config: Dict[str, Any]) -> str:
        """合并所有交通流文件"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        output_file = os.path.join(temp_dir, 'routes.rou.xml')
        
        # 获取所有需要合并的文件
        input_files = []
        
        # 添加背景交通流文件
        background_file = os.path.join(temp_dir, 'background.rou.xml')
        if os.path.exists(background_file):
            input_files.append(background_file)
        
        # 添加活动相关的交通流文件
        activity_prefixes = ['gym_vehicle', 'gym_people']
        for prefix in activity_prefixes:
            activity_file = os.path.join(temp_dir, f'{prefix}.rou.xml')
            if os.path.exists(activity_file):
                input_files.append(activity_file)
        
        # 创建合并后的根元素
        root = ET.Element('routes')
        
        # 如果是VIP场景，添加VIP车辆类型定义和VIP车辆
        if self.config_parser.is_vip_scenario(config):
            # 添加VIP车辆类型
            vip_type = ET.SubElement(root, 'vType')
            vip_type.set('id', 'vip_car')
            vip_type.set('vClass', 'custom1')  # 设置为custom1以便通过限行路段
            vip_type.set('color', '0,1,0')
            
            # 添加VIP车辆
            vip_vehicle = ET.SubElement(root, 'vehicle')
            vip_vehicle.set('id', 'vip')
            vip_vehicle.set('type', 'vip_car')
            vip_vehicle.set('depart', '0.00')
            
            # 添加VIP车辆路由
            vip_route = ET.SubElement(vip_vehicle, 'route')
            scenario_type = self.config_parser.get_scenario_type(config)
            if scenario_type == '进场':
                vip_route.set('edges', "1192568455#8 -1127568452#2 -1127568452#1 -1127568452#0 1127568453#0 1127568453#1 1127568453#2 1127568453#3 -1091134078#13 -1091134078#12 -1091134078#11 -1091134078#10 1225157743#6 1225157743#7 1225157743#8 1225157743#9 1225157743#11 1225157743#12 980398775#10 980398780#1 980398780#1.18 E0 E0.21 people_east_in")
            else:  # 离场
                vip_route.set('edges', "people_south_out -E11.15 -E11.33 -E11.57 -E11.84 -E11.114 -E11.145 -1307189273#2.153 -1307189273#2.250 -1307189273#1 1077433155#2 1077433155#4 1077433155#5 1077433155#6 1077433155#7 1091134078#10 1091134078#11 1091134078#12 1091134078#13 -1127568453#3 -1127568453#2 -1127568453#1 -1127568453#0 1127568452#0 1127568452#1 1127568452#2 1192568456#6")
        
        # 用于存储所有vehicle和person元素
        vehicles = []
        
        # 遍历所有输入文件
        for file_name in input_files:
            tree = ET.parse(file_name)
            for elem in tree.getroot():
                if elem.tag in ['vehicle', 'person']:
                    vehicles.append(elem)
                else:
                    # 非vehicle/person元素（如vType）直接添加到root
                    root.append(elem)
        
        # 按出发时间排序
        vehicles.sort(key=lambda v: float(v.get('depart')))
        
        # 将排序后的vehicle/person元素添加到root
        for vehicle in vehicles:
            root.append(vehicle)
        
        # 保存合并后的文件
        tree = ET.ElementTree(root)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
        
        return output_file
    
    def _add_parking_stops(self, route_file: str):
        """为体育馆车辆添加停车信息"""
        # 读取路由文件
        tree = ET.parse(route_file)
        root = tree.getroot()
        
        # 遍历所有vehicle节点
        for vehicle in root.findall('vehicle'):
            if vehicle.get('id').startswith('gym_vehicle'):
                route = vehicle.find('route')
                if route is not None:
                    edges = route.get('edges')
                    if edges and not vehicle.find('stop'):
                        last_edge = edges.split()[-1]
                        
                        # 检查是否是east_1到east_12或south_1到south_24
                        if last_edge.startswith('east_') and last_edge[5:].isdigit():
                            edge_num = int(last_edge[5:])
                            if 1 <= edge_num <= 12:
                                stop = ET.SubElement(vehicle, 'stop')
                                stop.set('parkingArea', f'pa_{last_edge}')
                                stop.set('duration', '3600')
                        
                        elif last_edge.startswith('south_') and last_edge[6:].isdigit():
                            edge_num = int(last_edge[6:])
                            if 1 <= edge_num <= 24:
                                stop = ET.SubElement(vehicle, 'stop')
                                stop.set('parkingArea', f'pa_{last_edge}')
                                stop.set('duration', '3600')
        
        # 保存修改后的文件
        tree.write(route_file, encoding='utf-8', xml_declaration=True)
    
    def _generate_sumo_command(self, sim_id: str, config: Dict[str, Any], route_file: str) -> list:
        """生成SUMO命令"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        
        # 获取路网文件 - 使用与交通流生成相同的逻辑
        net_file = self._get_final_network_file(sim_id, config)
        
        # 确定附加文件
        additional_files = [
            os.path.join(self.template_dir, 'osm_polygons.add.xml'),
            os.path.join(self.template_dir, 'parkinglot.add.xml')
        ]
        
        # 如果是离场场景，添加edge_data.add.xml并修改输出路径
        if self.config_parser.get_scenario_type(config) == '离场':
            # 读取原始edge_data.add.xml
            edge_data_template = os.path.join(self.template_dir, 'edge_data.add.xml')
            edge_data_output = os.path.join(temp_dir, 'edge_data.add.xml')
            
            # 解析XML并修改输出路径
            tree = ET.parse(edge_data_template)
            root = tree.getroot()
            
            # 修改两个edgeData元素的file属性
            for edge_data in root.findall('edgeData'):
                file_path = edge_data.get('file')
                if file_path:
                    file_name = os.path.basename(file_path)
                    edge_data.set('file', file_name)
            
            # 保存修改后的文件
            tree.write(edge_data_output, encoding='utf-8', xml_declaration=True)
            
            # 使用修改后的文件
            additional_files.append(edge_data_output)
        
        # 处理信号配时文件
        if self.config_parser.is_signal_optimization_enabled(config):
            # 生成自定义优化的配时方案
            optimized_signal_file = self._generate_optimized_signal_plan(sim_id, config, route_file)
            additional_files.append(optimized_signal_file)
        else:
            # 使用预设信号配时文件
            signal_files = self.config_parser.get_signal_files(config)
            additional_files.extend(signal_files)
        
        # 生成SUMO命令
        sumo_cmd = [
            'sumo-gui',
            '-n', net_file,
            '-r', route_file,
            '--additional-files', ",".join(additional_files),
            '--output-prefix', 'output.',
            '--summary-output', os.path.join(temp_dir, 'summary.xml'),
            '--person-summary-output', os.path.join(temp_dir, 'person.summary.xml'),
            '--tripinfo-output', os.path.join(temp_dir, 'tripinfo.xml'),
            '--tripinfo-output.write-unfinished', 'true',
            '--statistic-output', os.path.join(temp_dir, 'statistics.xml'),
            '--verbose', 'true',
            '--no-step-log', 'false',
            '--duration-log.statistics', 'true',
            '--duration-log.disable', 'false',
            '--no-warnings', 'false',
            '--seed', '42',
            '--begin', '0',
            '--end', '3600',
            '--step-length', '1',
            '--gui-settings-file', os.path.join(self.template_dir, 'style.xml'),
            '--ignore-route-errors', 'true'
        ]
        
        print(f"SUMO命令: {' '.join(sumo_cmd)}")
        return sumo_cmd
    
    def _get_final_network_file(self, sim_id: str, config: Dict[str, Any]) -> str:
        """获取最终用于仿真的路网文件"""
        # 如果启用道路限行，返回限行路网文件路径
        if self.config_parser.is_road_restriction_enabled(config):
            temp_dir = os.path.join(self.base_dir, sim_id)
            restricted_net_file = os.path.join(temp_dir, 'restricted_network.net.xml')
            # 检查文件是否存在，如果不存在则创建
            if not os.path.exists(restricted_net_file):
                original_net_file = self.config_parser.get_network_file(config)
                return self._create_restricted_network(sim_id, original_net_file, config)
            return restricted_net_file
        else:
            return self.config_parser.get_network_file(config)
    
    def _create_restricted_network(self, sim_id: str, original_net_file: str, config: Dict[str, Any]) -> str:
        """
        创建限行路网文件
        
        参数:
        sim_id: str - 仿真ID
        original_net_file: str - 原始路网文件路径
        config: dict - 配置信息
        
        返回:
        str - 限行路网文件路径
        """
        temp_dir = os.path.join(self.base_dir, sim_id)
        restricted_net_file = os.path.join(temp_dir, 'restricted_network.net.xml')
        
        # 获取限行路段列表
        restricted_edges = self.config_parser.get_restricted_edges(config)
        print(f"正在创建限行路网，限行路段: {restricted_edges}")
        
        try:
            # 解析原始路网文件
            tree = ET.parse(original_net_file)
            root = tree.getroot()
            
            # 修改限行路段的车道allow属性
            for edge in root.findall('edge'):
                edge_id = edge.get('id')
                if edge_id in restricted_edges:
                    print(f"设置路段 {edge_id} 为限行路段")
                    # 修改该路段的所有车道
                    for lane in edge.findall('lane'):
                        # 设置只允许VIP车辆通过
                        # 使用vClass="custom1"来标识VIP车辆
                        lane.set('allow', 'custom1 pedestrian')
                        # 禁止其他车辆类型
                        lane.set('disallow', 'passenger private taxi bus coach delivery truck trailer motorcycle moped bicycle')
            
            # 保存修改后的路网文件
            tree.write(restricted_net_file, encoding='utf-8', xml_declaration=True)
            print(f"限行路网文件已创建: {restricted_net_file}")
            
            return restricted_net_file
            
        except Exception as e:
            print(f"创建限行路网失败: {e}")
            # 如果创建失败，返回原始路网文件
            return original_net_file
    
    def _generate_optimized_signal_plan(self, sim_id: str, config: Dict[str, Any], route_file: str) -> str:
        """生成优化的信号配时方案"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        net_file = self.config_parser.get_network_file(config)
        
        # 获取需要优化的交叉口
        optimization_intersections = self.config_parser.get_optimization_intersections(config)
        
        # 获取所有交叉口ID（用于计算跳过的交叉口）
        all_tls_ids = self._get_all_tls_ids(net_file)
        skip_tls_ids = [tls_id for tls_id in all_tls_ids if tls_id not in optimization_intersections]
        
        # 生成优化后的配时文件
        optimized_file = os.path.join(temp_dir, 'optimized_signal.add.xml')
        
        # 构建tlsCycleAdaption.py命令
        cmd = [
            'python',
            'K:/project/太行院/250117_activity/tools/tlsCycleAdaptation.py',
            '-n', net_file,
            '-r', route_file,
            '-g', '12',
            '-o', optimized_file
        ]
        
        # 添加跳过的路口ID
        if skip_tls_ids:
            cmd.extend(['--skip', ','.join(skip_tls_ids)])
        
        try:
            # 执行优化程序
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"信号优化完成: {result.stdout}")
            return optimized_file
        except subprocess.CalledProcessError as e:
            print(f"信号优化失败: {e.stderr}")
            # 如果优化失败，返回默认配时文件
            return os.path.join(self.template_dir, 'newtls.add.xml')
    
    def _get_all_tls_ids(self, net_file_path: str) -> list:
        """从路网文件中获取所有路口ID"""
        if not os.path.exists(net_file_path):
            return []
        
        # 使用traci临时启动仿真来获取路口ID
        temp_cmd = ['sumo', '-n', net_file_path, '--no-step-log', '--no-warnings']
        
        try:
            # 启动临时仿真
            traci.start(temp_cmd)
            # 获取所有信号灯路口ID
            tls_ids = list(traci.trafficlight.getIDList())
            # 关闭临时仿真
            traci.close()
            return tls_ids
        except Exception as e:
            print(f"获取路口ID失败: {e}")
            # 确保关闭traci连接
            try:
                traci.close()
            except:
                pass
            return []
    
    def _run_sumo_simulation(self, sim_id: str, config: Dict[str, Any], sumo_cmd: list):
        """运行SUMO仿真"""
        # 启动仿真
        traci.start(sumo_cmd)
        
        try:
            if self.config_parser.is_vip_priority_enabled(config):
                # VIP优先通行仿真
                self._run_vip_simulation()
            else:
                # 普通仿真
                self._run_normal_simulation()
        finally:
            # 确保关闭仿真
            traci.close()
    
    def _run_vip_simulation(self):
        """运行VIP优先通行仿真"""
        vip_car_id = 'vip'
        last_real_edge = None
        distance_threshold = 50  # 距离路口50米时开始设置绿灯
        current_tls = None  # 当前正在控制的信号灯
        
        while traci.simulation.getTime() < 600:
            vehicle_ids = traci.vehicle.getIDList()
            if vip_car_id in vehicle_ids:
                current_edge = traci.vehicle.getRoadID(vip_car_id)
                
                # 如果当前在内部道路（路口内），使用上一个实际的edge
                if current_edge.startswith(':'):
                    if last_real_edge is None:
                        continue
                    current_edge = last_real_edge
                else:
                    last_real_edge = current_edge
                
                try:
                    route = traci.vehicle.getRoute(vip_car_id)
                    current_index = route.index(current_edge)
                    
                    if current_index < len(route) - 1:
                        next_edge = route[current_index + 1]
                        
                        # 获取当前edge通往的junction
                        to_junction = traci.edge.getToJunction(current_edge)
                        
                        # 获取车辆到路口的距离
                        remaining_dist = traci.vehicle.getLanePosition(vip_car_id)
                        lane_length = traci.lane.getLength(current_edge + "_0")
                        distance_to_tls = lane_length - remaining_dist
                        
                        try:
                            # 获取当前车道的所有控制连接
                            controlled_links = traci.trafficlight.getControlledLinks(to_junction)
                            if controlled_links:  # 如果是信号灯控制的路口
                                # 获取当前车道ID
                                current_lane = traci.vehicle.getLaneID(vip_car_id)
                                next_lane = next_edge + "_0"  # 假设使用目标edge的第一条车道
                                
                                # 在控制连接中找到当前路径对应的连接
                                for i, link_group in enumerate(controlled_links):
                                    for link in link_group:
                                        if link[0] == current_lane and link[1].split("_")[0] == next_edge:
                                            # 找到了对应的连接，i就是在相位中的索引位置
                                            if distance_to_tls <= distance_threshold:
                                                if current_tls != to_junction:
                                                    current_tls = to_junction
                                                
                                                # 获取当前相位的状态
                                                current_state = list(traci.trafficlight.getRedYellowGreenState(to_junction))
                                                
                                                # 如果当前不是绿灯，找到合适的绿灯相位
                                                if current_state[i] != 'G':
                                                    phases = traci.trafficlight.getAllProgramLogics(to_junction)[0].phases
                                                    for phase_index, phase in enumerate(phases):
                                                        if phase.state[i] == 'G':
                                                            traci.trafficlight.setPhase(to_junction, phase_index)
                                                            break
                                            elif current_tls == to_junction:
                                                current_tls = None
                                            break
                        except traci.exceptions.TraCIException:
                            # 路口没有信号灯，跳过处理
                            pass
                
                except ValueError:
                    print(f"--警告: edge {current_edge} 不在路由中")
                    continue
            
            traci.simulationStep()

    def _run_normal_simulation(self):
        """运行普通仿真"""
        while traci.simulation.getTime() < 600:
            traci.simulationStep() 