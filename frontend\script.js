document.addEventListener('DOMContentLoaded', function() {
    // 元素引用
    const netPreset = document.getElementById('net-preset');
    const netCustom = document.getElementById('net-custom');
    const uploadNetBtn = document.getElementById('uploadNetBtn');
    
    const trafficPreset = document.getElementById('traffic-preset');
    const trafficCustom = document.getElementById('traffic-custom');
    const uploadRouBtn = document.getElementById('uploadRouBtn');
    
    const vehicleType = document.getElementById('vehicleType');
    const entranceType = document.getElementById('entranceType');
    const vipPriority = document.getElementById('vipPriority');
    
    const signalPreset = document.getElementById('signal-preset');
    const signalCustom = document.getElementById('signal-custom');
    const uploadAddBtn = document.getElementById('uploadAddBtn');
    
    const roadRestriction = document.getElementById('roadRestriction');
    const signalOptimization = document.getElementById('signalOptimization');
    
    const runBtn = document.querySelector('.run-btn');
    const loadResultBtn = document.getElementById('loadResultBtn');
    
    // 存储上传的文件路径
    let uploadedNetFile = null;
    let uploadedRouFile = null;
    let uploadedAddFile = null;
    
    // 存储选中的限行路段和信控优化交叉口
    let selectedRestrictedEdges = [];
    let selectedIntersections = [];
    
    // 将变量暴露到全局作用域，供index.html中的函数使用
    window.selectedRestrictedEdges = selectedRestrictedEdges;
    window.selectedIntersections = selectedIntersections;
    
    // 初始状态设置
    updateEntranceTypeState();
    updateVipPriorityState();
    
    // 确保内容适应屏幕高度
    adjustContentToFitScreen();
    window.addEventListener('resize', adjustContentToFitScreen);
    
    // 事件监听器
    netPreset.addEventListener('change', updateEntranceTypeState);
    netCustom.addEventListener('change', updateEntranceTypeState);
    vehicleType.addEventListener('change', updateVipPriorityState);
    
    // 上传按钮事件
    uploadNetBtn.addEventListener('click', function() {
        if (netCustom.checked) {
            simulateFileUpload('net');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });
    
    uploadRouBtn.addEventListener('click', function() {
        if (trafficCustom.checked) {
            simulateFileUpload('rou');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });
    
    uploadAddBtn.addEventListener('click', function() {
        if (signalCustom.checked) {
            simulateFileUpload('add');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });
    
    // 加载已有结果按钮事件
    loadResultBtn.addEventListener('click', loadExistingResult);
    
    // 限行道路按钮事件
    roadRestriction.addEventListener('change', function() {
        if (this.checked) {
            selectRestrictedEdges();
        } else {
            selectedRestrictedEdges = [];
        }
    });
    
    // 信控优化按钮事件
    signalOptimization.addEventListener('change', function() {
        if (this.checked) {
            selectIntersections();
        } else {
            selectedIntersections = [];
        }
    });
    
    // 运行按钮事件
    runBtn.addEventListener('click', startSimulation);
    
    // 功能函数
    function updateEntranceTypeState() {
        entranceType.disabled = !netPreset.checked;
        if (!netPreset.checked) {
            entranceType.parentElement.parentElement.classList.add('disabled');
        } else {
            entranceType.parentElement.parentElement.classList.remove('disabled');
        }
    }
    
    function updateVipPriorityState() {
        const hasVIP = vehicleType.value === 'vip';
        vipPriority.disabled = !hasVIP;
        if (!hasVIP) {
            vipPriority.checked = false;
            vipPriority.parentElement.parentElement.classList.add('disabled');
        } else {
            vipPriority.parentElement.parentElement.classList.remove('disabled');
        }
    }
    
    function adjustContentToFitScreen() {
        const container = document.querySelector('.container');
        const windowHeight = window.innerHeight;
        const contentHeight = container.scrollHeight;
        
        // 如果内容高度超过窗口高度，调整容器的缩放比例
        if (contentHeight > windowHeight) {
            const scale = Math.min(0.95, windowHeight / contentHeight);
            container.style.transform = `scale(${scale})`;
            container.style.transformOrigin = 'top center';
            container.style.marginBottom = `${(windowHeight - contentHeight * scale) / 2}px`;
        } else {
            container.style.transform = '';
            container.style.marginBottom = '';
        }
    }
    
    function simulateFileUpload(fileType) {
        const input = document.createElement('input');
        input.type = 'file';
        
        let fileExtension = '';
        let acceptTypes = '';
        
        switch(fileType) {
            case 'net':
                fileExtension = '.net.xml';
                acceptTypes = '.net.xml';
                break;
            case 'rou':
                fileExtension = '.rou.xml';
                acceptTypes = '.rou.xml';
                break;
            case 'add':
                fileExtension = '.add.xml';
                acceptTypes = '.add.xml';
                break;
        }
        
        input.accept = acceptTypes;
        
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件扩展名
                if (!file.name.toLowerCase().endsWith(fileExtension)) {
                    showNotification(`文件类型错误，请选择${fileExtension}文件`, 'error');
                    return;
                }
                
                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('文件过大，请选择小于10MB的文件', 'error');
                    return;
                }
                
                // 验证文件内容（这里只是模拟，实际中可以读取文件内容进行更深入的验证）
                validateFileContent(file, fileType)
                    .then(isValid => {
                        if (isValid) {
                            // 存储文件路径（此处使用文件名模拟路径）
                            switch(fileType) {
                                case 'net':
                                    uploadedNetFile = file.name;
                                    break;
                                case 'rou':
                                    uploadedRouFile = file.name;
                                    break;
                                case 'add':
                                    uploadedAddFile = file.name;
                                    break;
                            }
                            
                            showNotification(`已选择文件: ${file.name}`, 'success');
                        } else {
                            showNotification(`文件内容无效，请选择有效的${fileExtension}文件`, 'error');
                        }
                    })
                    .catch(error => {
                        showNotification('文件验证过程中发生错误', 'error');
                        console.error(error);
                    });
            }
        };
        
        input.click();
    }
    
    // 加载已有结果文件
    function loadExistingResult() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件扩展名
                if (!file.name.toLowerCase().endsWith('.json')) {
                    showNotification('文件类型错误，请选择JSON格式文件', 'error');
                    return;
                }
                
                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('文件过大，请选择小于10MB的文件', 'error');
                    return;
                }
                
                // 读取JSON文件内容
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // 尝试解析JSON
                        const jsonData = JSON.parse(e.target.result);
                        
                        // 验证JSON结构
                        if (validateResultJson(jsonData)) {
                            showNotification('正在加载结果文件...', 'info');
                            
                            // 将JSON数据传递到结果页面
                            const encodedData = encodeURIComponent(JSON.stringify(jsonData));
                            window.location.href = `results.html?data=${encodedData}`;
                        } else {
                            showNotification('JSON格式无效，请提供有效的结果文件', 'error');
                        }
                    } catch (error) {
                        showNotification('无法解析JSON文件', 'error');
                        console.error('JSON解析错误:', error);
                    }
                };
                
                reader.onerror = function() {
                    showNotification('读取文件时发生错误', 'error');
                };
                
                reader.readAsText(file);
            }
        };
        
        input.click();
    }
    
    // 验证结果JSON的结构
    function validateResultJson(json) {
        // 检查必要的字段是否存在
        if (!json.simulation_id || !json.config_summary || !json.start_time || !json.simulation_results) {
            return false;
        }
        
        // 检查仿真结果字段
        const results = json.simulation_results;
        if (!results.pedestrian_metrics || !results.vehicle_metrics) {
            return false;
        }
        
        // 基本验证通过
        return true;
    }
    
    function validateFileContent(file, fileType) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const content = e.target.result;
                
                // 简单检查XML格式和关键标签
                let isValid = content.trim().startsWith('<?xml');
                
                if (isValid) {
                    switch(fileType) {
                        case 'net':
                            isValid = content.includes('<net') || content.includes('<network');
                            break;
                        case 'rou':
                            isValid = content.includes('<routes') || content.includes('<flows');
                            break;
                        case 'add':
                            isValid = content.includes('<additional') || content.includes('<additionals');
                            break;
                    }
                }
                
                resolve(isValid);
            };
            
            reader.onerror = function() {
                reject(new Error('文件读取错误'));
            };
            
            // 只读取文件开头一部分来检查，提高效率
            const blob = file.slice(0, 4096);
            reader.readAsText(blob);
        });
    }

    // API基础路径
    const API_BASE = 'http://localhost:8888';
    
    // 获取当前选择的路网文件路径
    function getCurrentNetFilePath() {
        if (netPreset.checked) {
            // 预设路网使用固定路径
            return 'sumo_data/templates/gym_tls.net.xml';
        } else if (netCustom.checked && uploadedNetFile) {
            // 自定义路网使用上传的文件路径
            // 注意：这里假设uploadedNetFile是服务器可访问的路径
            return uploadedNetFile;
        } else {
            // 默认使用预设路网
            return 'sumo_data/templates/gym_tls.net.xml';
        }
    }

    // 启动路段选择器（道路限行）
    async function selectRestrictedEdges() {
        try {
            showLoading('正在启动路段选择器...');
            
            const netFilePath = getCurrentNetFilePath();
            console.log('使用路网文件:', netFilePath);
            
            const response = await fetch(`${API_BASE}/api/network/select`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    net_file_path: netFilePath,
                    selector_type: 'edge'
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            hideLoading();
            
            if (result.success) {
                selectedRestrictedEdges = result.selected_ids || [];
                window.selectedRestrictedEdges = selectedRestrictedEdges; // 更新全局变量
                console.log(`用户选择了 ${result.count} 个限行路段:`, selectedRestrictedEdges);
                
                // 更新UI显示
                updateSelectionStatus('roadRestriction', result.count);
                
                if (result.count > 0) {
                    showNotification(`已选择 ${result.count} 个限行路段`, 'success');
                } else {
                    showNotification('未选择任何限行路段', 'info');
                }
            } else {
                showError(result.error || '路段选择失败');
                document.getElementById('roadRestriction').checked = false;
            }
        } catch (error) {
            hideLoading();
            console.error('路段选择器启动失败:', error);
            showError(`网络错误: ${error.message}`);
            document.getElementById('roadRestriction').checked = false;
        }
    }

    // 启动交叉口选择器（信号优化）
    async function selectIntersections() {
        try {
            showLoading('正在启动交叉口选择器...');
            
            const netFilePath = getCurrentNetFilePath();
            console.log('使用路网文件:', netFilePath);
            
            const response = await fetch(`${API_BASE}/api/network/select`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    net_file_path: netFilePath,
                    selector_type: 'intersection'
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            hideLoading();
            
            if (result.success) {
                selectedIntersections = result.selected_ids || [];
                window.selectedIntersections = selectedIntersections; // 更新全局变量
                console.log(`用户选择了 ${result.count} 个交叉口:`, selectedIntersections);
                
                // 更新UI显示
                updateSelectionStatus('signalOptimization', result.count);
                
                if (result.count > 0) {
                    showNotification(`已选择 ${result.count} 个优化交叉口`, 'success');
                } else {
                    showNotification('未选择任何优化交叉口', 'info');
                }
            } else {
                showError(result.error || '交叉口选择失败');
                document.getElementById('signalOptimization').checked = false;
            }
        } catch (error) {
            hideLoading();
            console.error('交叉口选择器启动失败:', error);
            showError(`网络错误: ${error.message}`);
            document.getElementById('signalOptimization').checked = false;
        }
    }

    // 更新选择状态UI
    function updateSelectionStatus(elementId, count) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const label = element.parentElement.parentElement.querySelector('label');
        if (!label) return;
        
        // 清除可能存在的旧状态显示
        const existingStatus = label.parentElement.querySelector('.selection-status');
        if (existingStatus) {
            existingStatus.remove();
        }
        
        // 如果有选择内容，添加状态显示
        if (count > 0) {
            const statusSpan = document.createElement('span');
            statusSpan.className = 'selection-status';
            statusSpan.style.marginLeft = '8px';
            statusSpan.style.fontSize = '12px';
            statusSpan.style.color = '#28a745';
            statusSpan.textContent = `(已选择 ${count} 项)`;
            label.parentElement.appendChild(statusSpan);
        }
    }

    // 启动仿真
    async function startSimulation() {
        try {
            showLoading('正在验证配置...');
            
            // 使用index.html中定义的buildConfigJSON函数构建配置
            const config = window.buildConfigJSON ? window.buildConfigJSON() : getDefaultConfig();
            console.log('发送配置:', config);
            
            // 首先验证配置
            const validateResponse = await fetch(`${API_BASE}/api/config/validate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            });
            
            const validateResult = await validateResponse.json();
            
            if (!validateResult.valid) {
                hideLoading();
                showError(`配置验证失败: ${validateResult.errors.map(e => e.error).join(', ')}`);
                return;
            }
            
            showLoading('正在启动仿真...');
            
            // 启动仿真
            const simResponse = await fetch(`${API_BASE}/api/start_simulation`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            });
            
            const simResult = await simResponse.json();
            hideLoading();
            
            if (simResult.success) {
                showSuccess('仿真执行成功!');
                
                // 如果有导航函数，使用它跳转到结果页面；否则只显示结果
                if (window.navigateToResultPage) {
                    window.navigateToResultPage(simResult);
                } else {
                displaySimulationResults(simResult);
                }
            } else {
                showError(`仿真失败: ${simResult.message}`);
            }
            
        } catch (error) {
            hideLoading();
            console.error('仿真请求失败:', error);
            showError('仿真请求失败，请检查服务器连接');
        }
    }

    // 显示加载状态
    function showLoading(message) {
        // 如果已有加载提示，则更新文本
        let loadingDiv = document.getElementById('loadingIndicator');
        
        if (!loadingDiv) {
            loadingDiv = document.createElement('div');
            loadingDiv.id = 'loadingIndicator';
            loadingDiv.style.position = 'fixed';
            loadingDiv.style.top = '50%';
            loadingDiv.style.left = '50%';
            loadingDiv.style.transform = 'translate(-50%, -50%)';
            loadingDiv.style.padding = '20px';
            loadingDiv.style.backgroundColor = 'rgba(0,0,0,0.7)';
            loadingDiv.style.color = 'white';
            loadingDiv.style.borderRadius = '8px';
            loadingDiv.style.zIndex = '9999';
            document.body.appendChild(loadingDiv);
        }
        
        loadingDiv.innerHTML = `
            <div style="text-align:center">
                <div class="spinner" style="border:4px solid rgba(255,255,255,0.3);border-radius:50%;border-top:4px solid white;width:40px;height:40px;margin:0 auto 10px;animation:spin 1s linear infinite;"></div>
                <div>${message}</div>
            </div>
        `;
        
        // 添加旋转动画
        if (!document.getElementById('spinnerStyle')) {
            const style = document.createElement('style');
            style.id = 'spinnerStyle';
            style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
            document.head.appendChild(style);
        }
    }

    // 隐藏加载状态
    function hideLoading() {
        const loadingDiv = document.getElementById('loadingIndicator');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }

    // 显示错误提示
    function showError(message) {
        alert(`错误: ${message}`);
    }

    // 显示成功提示
    function showSuccess(message) {
        alert(`成功: ${message}`);
    }

    // 显示仿真结果
    function displaySimulationResults(result) {
        // 这里可以实现显示仿真结果的逻辑
        console.log('仿真结果:', result);
        // 例如创建一个模态框来展示结果
    }
    
    // 通知提示
    function showNotification(message, type = 'info') {
        // 检查是否已存在通知容器
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
            
            // 添加通知容器样式
            const style = document.createElement('style');
            style.textContent = `
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                }
                .notification {
                    padding: 12px 20px;
                    margin-bottom: 10px;
                    border-radius: 4px;
                    color: white;
                    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
                    animation: slide-in 0.3s ease-out forwards;
                    max-width: 300px;
                }
                .notification.info {
                    background-color: var(--primary-color);
                }
                .notification.success {
                    background-color: var(--secondary-color);
                }
                .notification.error {
                    background-color: #ea4335;
                }
                @keyframes slide-in {
                    0% { transform: translateX(100%); opacity: 0; }
                    100% { transform: translateX(0); opacity: 1; }
                }
                @keyframes fade-out {
                    0% { transform: translateX(0); opacity: 1; }
                    100% { transform: translateX(100%); opacity: 0; }
                }
                .notification.fade-out {
                    animation: fade-out 0.3s ease-in forwards;
                }
            `;
            document.head.appendChild(style);
        }
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        container.appendChild(notification);
        
        // 3秒后移除通知
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                container.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // 默认配置（备用）
    function getDefaultConfig() {
        return {
            "network_config": {
                "type": "predefined",
                "file_path": null,
                "entrance_plan": "仅开放东侧出入口",
                "road_restriction": { 
                    "enabled": false, 
                    "restricted_edges": [] 
                }
            },
            "signal_config": {
                "type": "predefined",
                "file_path": null,
                "optimization": { 
                    "enabled": false, 
                    "selected_intersections": [] 
                }
            },
            "traffic_config": {
                "type": "predefined",
                "file_path": null,
                "scenario": "进场",
                "vehicle_type": "仅一般车辆",
                "vip_priority": { 
                    "enabled": false 
                }
            }
        };
    }
}); 