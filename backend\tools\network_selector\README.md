# 网络选择器工具

这是一个用于SUMO路网中选择交叉口和路段的可视化工具包。支持三种选择模式：

## 功能特性

- **交叉口选择器** - 专门用于选择信号灯控制的交叉口
- **路段选择器** - 专门用于选择道路路段
- **混合选择器** - 可以同时选择交叉口和路段

## 安装依赖

```bash
pip install flask
```

## 使用方法

### 1. 交叉口选择器

```python
from tools.network_selector import IntersectionSelector

# 创建选择器
selector = IntersectionSelector("path/to/your/network.net.xml")

# 运行选择器（会打开浏览器界面）
selected_intersections = selector.run(port=5001)

print(f"选择的交叉口: {selected_intersections}")
```

### 2. 路段选择器

```python
from tools.network_selector import EdgeSelector

# 创建选择器
selector = EdgeSelector("path/to/your/network.net.xml")

# 运行选择器
selected_edges = selector.run(port=5002)

print(f"选择的路段: {selected_edges}")
```

### 3. 混合选择器

```python
from tools.network_selector import MixedSelector

# 创建选择器
selector = MixedSelector("path/to/your/network.net.xml")

# 运行选择器
selected_items = selector.run(port=5003)

print(f"选择的元素: {selected_items}")
# 返回格式: {'intersections': [...], 'edges': [...]}
```

## 命令行使用

```bash
# 进入network_selector目录
cd tools/network_selector

# 运行示例程序
python example_usage.py intersection  # 交叉口选择器
python example_usage.py edge         # 路段选择器
python example_usage.py mixed        # 混合选择器
```

## 界面操作

- **鼠标点击** - 选择/取消选择元素
- **鼠标拖拽** - 移动地图视图
- **鼠标滚轮** - 缩放地图
- **缩放按钮** - 放大/缩小/重置视图
- **全选/清空** - 批量操作
- **确认选择** - 完成选择并返回结果

## 配置说明

每个选择器都有不同的显示配置：

- `IntersectionSelector`: 只显示和允许选择交叉口
- `EdgeSelector`: 显示交叉口和路段，但只允许选择路段
- `MixedSelector`: 显示和允许选择交叉口和路段

## 文件结构

```
network_selector/
├── __init__.py           # 包初始化
├── network_selector.py   # 主要选择器类
├── example_usage.py      # 使用示例
├── templates/
│   └── selector.html     # Web界面模板
├── static/
│   ├── selector.js       # 前端交互逻辑
│   └── style.css         # 样式文件
└── README.md            # 说明文档
```

## 返回值格式

- **IntersectionSelector**: 返回交叉口ID列表 `['junction1', 'junction2', ...]`
- **EdgeSelector**: 返回路段ID列表 `['edge1', 'edge2', ...]` 
- **MixedSelector**: 返回字典 `{'intersections': [...], 'edges': [...]}`

## 注意事项

1. 确保SUMO路网文件路径正确
2. 每个选择器使用不同的端口避免冲突
3. 选择完成后浏览器窗口会自动关闭
4. 程序会在终端显示选择结果 