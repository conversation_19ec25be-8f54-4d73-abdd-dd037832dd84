<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>{{ title }}</h1>
            <p>{{ description }}</p>
        </header>

        <div class="controls">
            <div class="info">
                <span id="totalCount">正在加载...</span>
                <span id="selectedCount">已选择: 0</span>
                {% if show_edges %}
                <label style="margin-left: 20px;">
                    <input type="checkbox" id="showEdges" checked> 显示路段
                </label>
                {% endif %}
            </div>
            <div class="buttons">
                <button id="selectAll">全选</button>
                <button id="clearAll">清空</button>
                <button id="confirmBtn" disabled>确认选择</button>
            </div>
        </div>

        <div class="main-content">
            <div class="map-container">
                <canvas id="networkCanvas"></canvas>
                
                <!-- 添加缩放控制按钮 -->
                <div class="zoom-controls">
                    <button class="zoom-btn" id="zoomIn" title="放大">+</button>
                    <button class="zoom-btn" id="zoomOut" title="缩小">−</button>
                    <button class="zoom-btn" id="zoomReset" title="重置">⌂</button>
                </div>
                
                <div id="tooltip" class="tooltip"></div>
                <div id="loading" class="loading">正在加载网络数据...</div>
            </div>

            <div class="sidebar">
                <h3>选中的元素</h3>
                <div id="selectedList" class="selected-list">
                    <p class="empty-message">请在地图上点击选择元素</p>
                </div>
                
                <div class="instructions">
                    <h4>使用说明:</h4>
                    <ul>
                        {% for instruction in instructions %}
                        <li>{{ instruction }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>

        <div id="status" class="status"></div>
    </div>

    <script src="/static/selector.js"></script>
    <script>
        // 传递配置到JavaScript
        window.SELECTOR_CONFIG = {
            "selector_type": "{{ selector_type }}",
            "show_intersections": {{ show_intersections|lower }},
            "show_edges": {{ show_edges|lower }},
            "allow_intersection_selection": {{ allow_intersection_selection|lower }},
            "allow_edge_selection": {{ allow_edge_selection|lower }},
            "primary_object": "{{ primary_object }}"
        };

        // 页面加载完成后自动加载数据
        window.addEventListener('load', () => {
            const selector = new NetworkSelector();
        });
    </script>
</body>
</html> 