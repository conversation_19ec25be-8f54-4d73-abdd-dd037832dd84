import xml.etree.ElementTree as ET
import json
import os
import threading
import time
import webbrowser
from flask import Flask, render_template, jsonify, request, send_from_directory
import tempfile
import sys
import requests
from abc import ABC, abstractmethod
from werkzeug.serving import make_server

class BaseNetworkSelector(ABC):
    """基础网络选择器类，提供通用功能"""
    
    def __init__(self, net_file_path, title="网络选择器"):
        self.net_file_path = net_file_path
        self.title = title
        self.selected_ids = []
        self.server = None
        self.server_thread = None
        self.selection_complete = threading.Event()
        
        # 使用绝对路径来确保能找到模板和静态文件
        base_dir = os.path.dirname(os.path.abspath(__file__))
        template_folder = os.path.join(base_dir, 'templates')
        static_folder = os.path.join(base_dir, 'static')
        
        # 检查目录是否存在
        if not os.path.exists(template_folder):
            print(f"❌ 模板目录不存在: {template_folder}")
        else:
            print(f"✅ 模板目录: {template_folder}")
            
        if not os.path.exists(static_folder):
            print(f"❌ 静态文件目录不存在: {static_folder}")
        else:
            print(f"✅ 静态文件目录: {static_folder}")
        
        self.app = Flask(__name__, 
                        template_folder=template_folder,
                        static_folder=static_folder)
        self.setup_routes()
        
    def parse_network_data(self):
        """解析路网文件获取交叉口和路段信息"""
        if not os.path.exists(self.net_file_path):
            print(f"路网文件不存在: {self.net_file_path}")
            return {'intersections': [], 'edges': [], 'bounds': {}}
            
        intersections = []
        edges = []
        
        try:
            tree = ET.parse(self.net_file_path)
            root = tree.getroot()
            
            # 解析交叉口
            for junction in root.findall('junction'):
                if junction.get('type') == 'traffic_light':
                    intersection = {
                        'id': junction.get('id'),
                        'x': float(junction.get('x')),
                        'y': float(junction.get('y')),
                        'selected': False
                    }
                    intersections.append(intersection)
            
            # 解析路段
            for edge in root.findall('edge'):
                edge_id = edge.get('id')
                # 跳过内部路段（以冒号开头）
                if edge_id and not edge_id.startswith(':'):
                    from_junction = edge.get('from')
                    to_junction = edge.get('to')
                    edge_name = edge.get('name', '')  # 获取路段名称，如果没有则为空字符串
                    
                    # 获取路段的几何形状
                    lanes = edge.findall('lane')
                    if lanes:
                        # 使用第一条车道的形状
                        first_lane = lanes[0]
                        shape = first_lane.get('shape')
                        
                        if shape:
                            # 解析shape坐标
                            points = []
                            for point_str in shape.split():
                                try:
                                    x, y = map(float, point_str.split(','))
                                    points.append({'x': x, 'y': y})
                                except:
                                    continue
                            
                            if len(points) >= 2:
                                edge_info = {
                                    'id': edge_id,
                                    'name': edge_name,  # 添加name字段
                                    'from': from_junction,
                                    'to': to_junction,
                                    'points': points
                                }
                                edges.append(edge_info)
            
            # 计算边界框
            all_points = []
            for intersection in intersections:
                all_points.extend([intersection['x'], intersection['y']])
            
            for edge in edges:
                for point in edge['points']:
                    all_points.extend([point['x'], point['y']])
            
            if all_points:
                x_coords = all_points[::2]  # 奇数索引是x坐标
                y_coords = all_points[1::2]  # 偶数索引是y坐标
                
                bounds = {
                    'min_x': min(x_coords),
                    'max_x': max(x_coords),
                    'min_y': min(y_coords),
                    'max_y': max(y_coords)
                }
            else:
                bounds = {}
                
            print(f"成功解析到 {len(intersections)} 个交叉口, {len(edges)} 条路段")
            return {
                'intersections': intersections,
                'edges': edges,
                'bounds': bounds
            }
                    
        except Exception as e:
            print(f"解析路网文件错误: {e}")
            return {'intersections': [], 'edges': [], 'bounds': {}}
    
    @abstractmethod
    def get_selector_config(self):
        """获取选择器配置，子类需要实现"""
        pass
        
    def setup_routes(self):
        """设置Flask路由"""
        
        @self.app.route('/')
        def index():
            config = self.get_selector_config()
            return render_template('selector.html', **config)
        
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            return send_from_directory(self.app.static_folder, filename)
            
        @self.app.route('/api/network_data')
        def get_network_data():
            network_data = self.parse_network_data()
            config = self.get_selector_config()
            
            return jsonify({
                'intersections': network_data['intersections'],
                'edges': network_data['edges'],
                'bounds': network_data['bounds'],
                'intersection_count': len(network_data['intersections']),
                'edge_count': len(network_data['edges']),
                'config': config
            })
        
        @self.app.route('/api/confirm_selection', methods=['POST'])
        def confirm_selection():
            data = request.json
            selected_intersection_ids = data.get('selected_intersection_ids', [])
            selected_edge_ids = data.get('selected_edge_ids', [])
            
            # 根据选择器类型确定使用哪个ID列表
            config = self.get_selector_config()
            if config['selector_type'] == 'intersection':
                self.selected_ids = selected_intersection_ids
                print(f"\n=== 交叉口选择结果 ===")
                print(f"选择的交叉口数量: {len(selected_intersection_ids)}")
                print(f"交叉口ID列表: {selected_intersection_ids}")
            elif config['selector_type'] == 'edge':
                self.selected_ids = selected_edge_ids
                print(f"\n=== 路段选择结果 ===")
                print(f"选择的路段数量: {len(selected_edge_ids)}")
                print(f"路段ID列表: {selected_edge_ids}")
            else:  # 混合模式
                self.selected_ids = {
                    'intersections': selected_intersection_ids,
                    'edges': selected_edge_ids
                }
                print(f"\n=== 混合选择结果 ===")
                print(f"选择的交叉口数量: {len(selected_intersection_ids)}")
                print(f"交叉口ID列表: {selected_intersection_ids}")
                print(f"选择的路段数量: {len(selected_edge_ids)}")
                print(f"路段ID列表: {selected_edge_ids}")
            
            print("=" * 30)
            
            self.selection_complete.set()
            
            return jsonify({
                'success': True,
                'message': f'选择已确认，页面将在2秒后关闭'
            })
    
    def start_server(self, port=5001):
        """启动Flask服务器（非阻塞）"""
        try:
            # 创建服务器实例
            self.server = make_server('localhost', port, self.app, threaded=True)
            
            # 在单独线程中运行服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            print(f"✅ Flask服务器已启动在端口 {port}")
            
            # 等待一小段时间确保服务器启动
            time.sleep(1)
            
            # 尝试验证服务器是否正常启动
            try:
                response = requests.get(f'http://localhost:{port}/', timeout=5)
                if response.status_code == 200:
                    print(f"✅ 服务器验证成功，状态码: {response.status_code}")
                    return True
                else:
                    print(f"❌ 服务器响应异常，状态码: {response.status_code}")
                    return False
            except requests.RequestException as e:
                print(f"❌ 服务器验证失败: {e}")
                return False
                
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
            return False
    
    def stop_server(self):
        """停止Flask服务器"""
        if self.server:
            print("🔄 正在关闭Flask服务器...")
            self.server.shutdown()
            if self.server_thread:
                self.server_thread.join(timeout=5)
            print("✅ Flask服务器已关闭")
    
    def run(self, port=5001):
        """运行选择器（非阻塞）"""
        print(f"=== {self.title} ===")
        print(f"正在解析路网文件: {self.net_file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(self.net_file_path):
            print(f"❌ 错误: 路网文件不存在 - {self.net_file_path}")
            return []
        
        # 启动Flask服务器
        print("🔄 正在启动Web服务器...")
        if not self.start_server(port):
            print("❌ 服务器启动失败")
            return []
        
        # 打开浏览器
        try:
            print(f"🌐 正在打开浏览器: http://localhost:{port}")
            webbrowser.open(f'http://localhost:{port}')
        except Exception as e:
            print(f"❌ 无法打开浏览器: {e}")
        
        # 等待用户选择完成
        print("⏳ 等待用户选择...")
        self.selection_complete.wait(timeout=300)  # 最多等待5分钟
        
        # 停止服务器
        self.stop_server()
        
        print(f"✅ 选择器已关闭，返回选择结果: {len(self.selected_ids) if isinstance(self.selected_ids, list) else self.selected_ids}")
        return self.selected_ids


class IntersectionSelector(BaseNetworkSelector):
    """交叉口选择器"""
    
    def __init__(self, net_file_path):
        super().__init__(net_file_path, "交叉口选择器")
    
    def get_selector_config(self):
        return {
            'title': '🚦 交叉口选择器',
            'description': '点击地图上的蓝色圆圈选择需要优化的交叉口',
            'selector_type': 'intersection',
            'show_intersections': True,
            'show_edges': True,  # 显示路段以帮助用户定位
            'allow_intersection_selection': True,
            'allow_edge_selection': False,  # 路段不可选择
            'primary_object': 'intersection',
            'instructions': [
                '🚦 点击蓝色圆圈选择交叉口',
                '🔴 红色表示已选择',
                '🔍 鼠标悬停查看ID',
                '🔍 使用右上角按钮缩放地图',
                '🖱️ 拖拽地图移动视图',
                '✅ 选择完成后点击"确认选择"'
            ]
        }


class EdgeSelector(BaseNetworkSelector):
    """路段选择器"""
    
    def __init__(self, net_file_path):
        super().__init__(net_file_path, "路段选择器")
    
    def get_selector_config(self):
        return {
            'title': '🛣️ 路段选择器',
            'description': '点击地图上的灰色线条选择需要限行的路段',
            'selector_type': 'edge',
            'show_intersections': True,
            'show_edges': True,
            'allow_intersection_selection': False,
            'allow_edge_selection': True,
            'primary_object': 'edge',
            'hide_intersection_labels': True,  # 隐藏交叉口ID标签，避免干扰
            'instructions': [
                '🛣️ 点击灰色线条选择路段',
                '🔴 红色表示已选择',
                '🔍 鼠标悬停查看ID',
                '🔍 使用右上角按钮缩放地图',
                '🖱️ 拖拽地图移动视图',
                '✅ 选择完成后点击"确认选择"'
            ]
        }


class MixedSelector(BaseNetworkSelector):
    """混合选择器（交叉口和路段都可选）"""
    
    def __init__(self, net_file_path):
        super().__init__(net_file_path, "网络元素选择器")
    
    def get_selector_config(self):
        return {
            'title': '🚦🛣️ 网络元素选择器',
            'description': '选择交叉口和路段进行优化',
            'selector_type': 'mixed',
            'show_intersections': True,
            'show_edges': True,
            'allow_intersection_selection': True,
            'allow_edge_selection': True,
            'primary_object': 'mixed',
            'instructions': [
                '🚦 点击蓝色圆圈选择交叉口',
                '🛣️ 点击灰色线条选择路段',
                '🔴 红色表示已选择',
                '🔍 鼠标悬停查看ID',
                '🔍 使用右上角按钮缩放地图',
                '🖱️ 拖拽地图移动视图',
                '✅ 选择完成后点击"确认选择"'
            ]
        } 