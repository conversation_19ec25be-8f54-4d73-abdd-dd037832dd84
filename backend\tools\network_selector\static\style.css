* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    overflow: hidden; /* 防止页面滚动 */
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    display: flex;
    flex-direction: column;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: none; /* 移除最大宽度限制 */
    margin: 0;
    padding: 10px;
}

header {
    text-align: center;
    margin-bottom: 15px;
    flex-shrink: 0; /* 防止压缩 */
}

header h1 {
    color: #2c3e50;
    font-size: 2em; /* 稍微减小字体 */
    margin-bottom: 5px;
}

header p {
    color: #7f8c8d;
    font-size: 1em;
}

.controls {
    background: white;
    padding: 12px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0; /* 防止压缩 */
}

.info {
    display: flex;
    gap: 20px;
}

.info span {
    padding: 6px 12px;
    background: #ecf0f1;
    border-radius: 5px;
    font-weight: bold;
    color: #2c3e50;
    font-size: 14px;
}

.buttons {
    display: flex;
    gap: 10px;
}

button {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

button:not(:disabled) {
    background: #3498db;
    color: white;
}

button:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-1px);
}

button:disabled {
    background: #bdc3c7;
    color: #7f8c8d;
    cursor: not-allowed;
}

#confirmBtn:not(:disabled) {
    background: #27ae60;
}

#confirmBtn:hover:not(:disabled) {
    background: #229954;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 15px;
    flex: 1; /* 占满剩余空间 */
    min-height: 0; /* 允许子元素收缩 */
}

.map-container {
    position: relative;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    min-height: 0; /* 允许收缩 */
}

#networkCanvas {
    width: 100%;
    height: 100%;
    cursor: pointer;
    display: block;
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px 30px;
    border-radius: 10px;
    font-size: 16px;
    color: #2c3e50;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 13px;
    pointer-events: none;
    z-index: 1000;
    display: none;
    white-space: nowrap;
}

/* 添加缩放控制按钮样式 */
.zoom-controls {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 100;
}

.zoom-btn {
    width: 35px;
    height: 35px;
    background: white;
    border: 2px solid #3498db;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    color: #3498db;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    background: #3498db;
    color: white;
    transform: scale(1.1);
}

.sidebar {
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    min-height: 0; /* 允许收缩 */
}

.sidebar h3 {
    color: #2c3e50;
    margin-bottom: 12px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 8px;
    font-size: 16px;
}

.selected-list {
    margin-bottom: 15px;
    max-height: 250px;
    overflow-y: auto;
}

.empty-message {
    color: #7f8c8d;
    font-style: italic;
    text-align: center;
    padding: 15px 10px;
    font-size: 13px;
}

.selected-item {
    background: #e8f5e8;
    border: 1px solid #27ae60;
    border-radius: 5px;
    padding: 8px 12px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.selected-item .remove-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 3px 6px;
    font-size: 10px;
    cursor: pointer;
}

.instructions {
    border-top: 2px solid #ecf0f1;
    padding-top: 12px;
}

.instructions h4 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 13px;
}

.instructions ul {
    list-style: none;
    padding-left: 0;
}

.instructions li {
    margin-bottom: 6px;
    font-size: 12px;
    color: #555;
    line-height: 1.3;
}

.status {
    margin-top: 10px;
    padding: 10px 15px;
    border-radius: 5px;
    text-align: center;
    font-weight: bold;
    display: none;
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    min-width: 300px;
}

.status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background: #cce7ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr 250px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 5px;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .sidebar {
        max-height: 200px;
    }
    
    .controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .info {
        justify-content: center;
    }
}

.selected-item.edge-item {
    background: #fdf2e9;
    border: 1px solid #e67e22;
}

.selected-item.intersection-item {
    background: #e8f5e8;
    border: 1px solid #27ae60;
}

.selected-list h4 {
    margin: 10px 0 5px 0;
    font-size: 13px;
    padding: 5px 10px;
    border-radius: 3px;
    background: #f8f9fa;
}
